import { NextRequest, NextResponse } from 'next/server';
import { generateFeedback, generateHint } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, ...params } = body;

    if (type === 'feedback') {
      const { levelTitle, userAnswers, correctAnswers, score, maxScore } = params;
      
      const feedback = await generateFeedback(
        levelTitle,
        userAnswers,
        correctAnswers,
        score,
        maxScore
      );

      return NextResponse.json({
        success: true,
        data: feedback
      });
    }

    if (type === 'hint') {
      const { question, context, difficulty } = params;
      
      const hint = await generateHint(question, context, difficulty);

      return NextResponse.json({
        success: true,
        data: { hint }
      });
    }

    return NextResponse.json(
      { error: 'Type de requête non supporté' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Erreur lors du test IA:', error);
    
    // Retourner un feedback de fallback
    return NextResponse.json({
      success: false,
      error: 'Erreur lors de la génération IA',
      fallback: {
        overallFeedback: "Continuez vos efforts ! L'IA n'est pas disponible actuellement.",
        strengths: ["Vous avez tenté de répondre aux questions"],
        improvements: ["Revoyez les concepts de base"],
        recommendations: ["Consultez les ressources pédagogiques"],
        nextLevelSuggestion: null
      }
    });
  }
}

// Endpoint pour tester la configuration OpenAI
export async function GET() {
  try {
    // Test simple de l'API OpenAI
    const testFeedback = await generateFeedback(
      "Test de configuration",
      [{ answer: "42", pointsEarned: 10 }],
      [{ correctAnswer: "42" }],
      10,
      10
    );

    return NextResponse.json({
      success: true,
      message: "Configuration OpenAI fonctionnelle",
      data: testFeedback
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Erreur de configuration OpenAI",
      error: error instanceof Error ? error.message : "Erreur inconnue",
      suggestion: "Vérifiez votre clé API OpenAI dans les variables d'environnement"
    });
  }
}
