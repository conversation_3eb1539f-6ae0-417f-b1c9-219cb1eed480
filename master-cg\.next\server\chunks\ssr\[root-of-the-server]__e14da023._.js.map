{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/CG/master-cg/src/types/index.ts"], "sourcesContent": ["// Types pour la plateforme Master CG\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  avatar?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  progress: UserProgress;\n  achievements: Achievement[];\n}\n\nexport interface UserProgress {\n  id: string;\n  userId: string;\n  currentLevel: number;\n  totalScore: number;\n  completedLevels: number[];\n  unlockedLevels: number[];\n  streakDays: number;\n  lastPlayedAt: Date;\n  totalTimeSpent: number; // en minutes\n}\n\nexport interface Level {\n  id: number;\n  title: string;\n  description: string;\n  category: LevelCategory;\n  difficulty: Difficulty;\n  estimatedTime: number; // en minutes\n  prerequisites: number[]; // IDs des niveaux prérequis\n  scenario: string;\n  context: LevelContext;\n  questions: Question[];\n  maxScore: number;\n  hints: string[];\n  resources: Resource[];\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface LevelContext {\n  company: string;\n  sector: string;\n  situation: string;\n  documents: Document[];\n  data: Record<string, any>;\n}\n\nexport interface Document {\n  id: string;\n  name: string;\n  type: DocumentType;\n  content: string;\n  url?: string;\n}\n\nexport interface Question {\n  id: string;\n  type: QuestionType;\n  question: string;\n  options?: string[]; // Pour les QCM\n  correctAnswer: string | string[];\n  explanation: string;\n  points: number;\n  tolerance?: number; // Pour les réponses numériques\n}\n\nexport interface UserAnswer {\n  questionId: string;\n  answer: string | string[];\n  isCorrect: boolean;\n  pointsEarned: number;\n  timeSpent: number; // en secondes\n  hintsUsed: number;\n}\n\nexport interface LevelAttempt {\n  id: string;\n  userId: string;\n  levelId: number;\n  answers: UserAnswer[];\n  totalScore: number;\n  maxScore: number;\n  percentage: number;\n  timeSpent: number; // en secondes\n  hintsUsed: number;\n  completed: boolean;\n  startedAt: Date;\n  completedAt?: Date;\n  feedback: AIFeedback;\n}\n\nexport interface AIFeedback {\n  id: string;\n  attemptId: string;\n  overallFeedback: string;\n  strengths: string[];\n  improvements: string[];\n  recommendations: string[];\n  nextLevelSuggestion?: number;\n  generatedAt: Date;\n}\n\nexport interface Achievement {\n  id: string;\n  name: string;\n  description: string;\n  icon: string;\n  category: AchievementCategory;\n  condition: AchievementCondition;\n  reward: number; // points bonus\n  unlockedAt?: Date;\n}\n\nexport interface Resource {\n  id: string;\n  title: string;\n  type: ResourceType;\n  content: string;\n  url?: string;\n  downloadable: boolean;\n}\n\n// Enums\nexport enum LevelCategory {\n  FONDAMENTAUX = 'FONDAMENTAUX',\n  BUDGET_CLOTURE = 'BUDGET_CLOTURE',\n  ANALYSE_DECISION = 'ANALYSE_DECISION',\n  CONTROLE_INDUSTRIEL = 'CONTROLE_INDUSTRIEL',\n  PILOTAGE_STRATEGIQUE = 'PILOTAGE_STRATEGIQUE'\n}\n\nexport enum Difficulty {\n  DEBUTANT = 'DEBUTANT',\n  INTERMEDIAIRE = 'INTERMEDIAIRE',\n  AVANCE = 'AVANCE',\n  EXPERT = 'EXPERT'\n}\n\nexport enum QuestionType {\n  QCM = 'QCM',\n  QCM_MULTIPLE = 'QCM_MULTIPLE',\n  NUMERIQUE = 'NUMERIQUE',\n  TEXTE_LIBRE = 'TEXTE_LIBRE',\n  CALCUL = 'CALCUL',\n  TABLEAU = 'TABLEAU'\n}\n\nexport enum DocumentType {\n  BALANCE = 'BALANCE',\n  COMPTE_RESULTAT = 'COMPTE_RESULTAT',\n  BUDGET = 'BUDGET',\n  FACTURE = 'FACTURE',\n  CONTRAT = 'CONTRAT',\n  RAPPORT = 'RAPPORT',\n  TABLEAU_BORD = 'TABLEAU_BORD'\n}\n\nexport enum AchievementCategory {\n  PROGRESSION = 'PROGRESSION',\n  PERFORMANCE = 'PERFORMANCE',\n  REGULARITE = 'REGULARITE',\n  EXPERTISE = 'EXPERTISE',\n  SOCIAL = 'SOCIAL'\n}\n\nexport enum ResourceType {\n  COURS = 'COURS',\n  EXERCICE = 'EXERCICE',\n  TEMPLATE = 'TEMPLATE',\n  VIDEO = 'VIDEO',\n  ARTICLE = 'ARTICLE'\n}\n\nexport interface AchievementCondition {\n  type: 'LEVELS_COMPLETED' | 'SCORE_THRESHOLD' | 'STREAK_DAYS' | 'CATEGORY_MASTERY' | 'PERFECT_SCORE';\n  value: number;\n  category?: LevelCategory;\n}\n\n// Types pour les statistiques et analytics\nexport interface UserStats {\n  totalLevelsCompleted: number;\n  averageScore: number;\n  totalTimeSpent: number;\n  currentStreak: number;\n  longestStreak: number;\n  categoryProgress: Record<LevelCategory, CategoryProgress>;\n  recentActivity: ActivityEntry[];\n}\n\nexport interface CategoryProgress {\n  category: LevelCategory;\n  completedLevels: number;\n  totalLevels: number;\n  averageScore: number;\n  timeSpent: number;\n}\n\nexport interface ActivityEntry {\n  id: string;\n  type: 'LEVEL_COMPLETED' | 'ACHIEVEMENT_UNLOCKED' | 'STREAK_MILESTONE';\n  description: string;\n  timestamp: Date;\n  points?: number;\n}\n\n// Types pour l'API\nexport interface APIResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Types pour les composants UI\nexport interface GameState {\n  currentLevel: Level | null;\n  currentAttempt: LevelAttempt | null;\n  isLoading: boolean;\n  error: string | null;\n  timeRemaining?: number;\n  hintsRemaining: number;\n}\n\nexport interface NavigationItem {\n  id: string;\n  label: string;\n  href: string;\n  icon: string;\n  badge?: number;\n  active?: boolean;\n}\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;;;AA+H9B,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,sCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,sCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,6CAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,sCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/CG/master-cg/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { LevelCategory, Difficulty } from \"@/types\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Formatage du temps\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const remainingSeconds = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}h ${minutes}m ${remainingSeconds}s`\n  } else if (minutes > 0) {\n    return `${minutes}m ${remainingSeconds}s`\n  } else {\n    return `${remainingSeconds}s`\n  }\n}\n\n// Formatage du temps en minutes\nexport function formatTimeMinutes(minutes: number): string {\n  const hours = Math.floor(minutes / 60)\n  const remainingMinutes = minutes % 60\n\n  if (hours > 0) {\n    return `${hours}h ${remainingMinutes}m`\n  } else {\n    return `${remainingMinutes}m`\n  }\n}\n\n// Calcul du pourcentage\nexport function calculatePercentage(score: number, maxScore: number): number {\n  if (maxScore === 0) return 0\n  return Math.round((score / maxScore) * 100)\n}\n\n// Détermination du niveau de performance\nexport function getPerformanceLevel(percentage: number): {\n  level: string\n  color: string\n  description: string\n} {\n  if (percentage >= 90) {\n    return {\n      level: \"Excellent\",\n      color: \"text-green-600\",\n      description: \"Performance exceptionnelle !\"\n    }\n  } else if (percentage >= 80) {\n    return {\n      level: \"Très bien\",\n      color: \"text-blue-600\",\n      description: \"Très bonne maîtrise\"\n    }\n  } else if (percentage >= 70) {\n    return {\n      level: \"Bien\",\n      color: \"text-yellow-600\",\n      description: \"Bonne compréhension\"\n    }\n  } else if (percentage >= 60) {\n    return {\n      level: \"Passable\",\n      color: \"text-orange-600\",\n      description: \"Compréhension correcte\"\n    }\n  } else {\n    return {\n      level: \"À améliorer\",\n      color: \"text-red-600\",\n      description: \"Nécessite plus de travail\"\n    }\n  }\n}\n\n// Couleurs pour les catégories\nexport function getCategoryColor(category: LevelCategory): string {\n  switch (category) {\n    case LevelCategory.FONDAMENTAUX:\n      return \"bg-blue-100 text-blue-800\"\n    case LevelCategory.BUDGET_CLOTURE:\n      return \"bg-green-100 text-green-800\"\n    case LevelCategory.ANALYSE_DECISION:\n      return \"bg-purple-100 text-purple-800\"\n    case LevelCategory.CONTROLE_INDUSTRIEL:\n      return \"bg-orange-100 text-orange-800\"\n    case LevelCategory.PILOTAGE_STRATEGIQUE:\n      return \"bg-red-100 text-red-800\"\n    default:\n      return \"bg-gray-100 text-gray-800\"\n  }\n}\n\n// Couleurs pour les difficultés\nexport function getDifficultyColor(difficulty: Difficulty): string {\n  switch (difficulty) {\n    case Difficulty.DEBUTANT:\n      return \"bg-green-100 text-green-800\"\n    case Difficulty.INTERMEDIAIRE:\n      return \"bg-yellow-100 text-yellow-800\"\n    case Difficulty.AVANCE:\n      return \"bg-orange-100 text-orange-800\"\n    case Difficulty.EXPERT:\n      return \"bg-red-100 text-red-800\"\n    default:\n      return \"bg-gray-100 text-gray-800\"\n  }\n}\n\n// Traduction des catégories\nexport function translateCategory(category: LevelCategory): string {\n  switch (category) {\n    case LevelCategory.FONDAMENTAUX:\n      return \"Fondamentaux\"\n    case LevelCategory.BUDGET_CLOTURE:\n      return \"Budget & Clôture\"\n    case LevelCategory.ANALYSE_DECISION:\n      return \"Analyse & Décision\"\n    case LevelCategory.CONTROLE_INDUSTRIEL:\n      return \"Contrôle Industriel\"\n    case LevelCategory.PILOTAGE_STRATEGIQUE:\n      return \"Pilotage Stratégique\"\n    default:\n      return \"Autre\"\n  }\n}\n\n// Traduction des difficultés\nexport function translateDifficulty(difficulty: Difficulty): string {\n  switch (difficulty) {\n    case Difficulty.DEBUTANT:\n      return \"Débutant\"\n    case Difficulty.INTERMEDIAIRE:\n      return \"Intermédiaire\"\n    case Difficulty.AVANCE:\n      return \"Avancé\"\n    case Difficulty.EXPERT:\n      return \"Expert\"\n    default:\n      return \"Non défini\"\n  }\n}\n\n// Validation des réponses numériques avec tolérance\nexport function validateNumericAnswer(\n  userAnswer: string,\n  correctAnswer: string,\n  tolerance: number = 0\n): boolean {\n  const userNum = parseFloat(userAnswer.replace(/[^\\d.-]/g, ''))\n  const correctNum = parseFloat(correctAnswer.replace(/[^\\d.-]/g, ''))\n  \n  if (isNaN(userNum) || isNaN(correctNum)) {\n    return false\n  }\n  \n  if (tolerance === 0) {\n    return userNum === correctNum\n  }\n  \n  const diff = Math.abs(userNum - correctNum)\n  const toleranceValue = Math.abs(correctNum * tolerance / 100)\n  \n  return diff <= toleranceValue\n}\n\n// Génération d'un ID unique\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\n// Calcul du niveau suivant recommandé\nexport function getNextRecommendedLevel(\n  currentLevel: number,\n  score: number,\n  maxScore: number,\n  completedLevels: number[]\n): number | null {\n  const percentage = calculatePercentage(score, maxScore)\n  \n  // Si le score est excellent (>= 85%), suggérer un niveau plus avancé\n  if (percentage >= 85) {\n    // Chercher le prochain niveau non complété dans une catégorie plus avancée\n    const nextAdvancedLevel = currentLevel + 10\n    if (nextAdvancedLevel <= 300 && !completedLevels.includes(nextAdvancedLevel)) {\n      return nextAdvancedLevel\n    }\n  }\n  \n  // Sinon, suggérer le niveau suivant logique\n  const nextLevel = currentLevel + 1\n  if (nextLevel <= 300 && !completedLevels.includes(nextLevel)) {\n    return nextLevel\n  }\n  \n  return null\n}\n\n// Calcul des statistiques de progression\nexport function calculateProgressStats(completedLevels: number[], totalLevels: number = 300) {\n  const completed = completedLevels.length\n  const percentage = (completed / totalLevels) * 100\n  \n  return {\n    completed,\n    total: totalLevels,\n    percentage: Math.round(percentage),\n    remaining: totalLevels - completed\n  }\n}\n\n// Formatage des nombres avec séparateurs\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('fr-FR').format(num)\n}\n\n// Formatage des devises\nexport function formatCurrency(amount: number, currency: string = 'EUR'): string {\n  return new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: currency\n  }).format(amount)\n}\n\n// Debounce function pour les recherches\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,QAAQ,KAAK;AACtB;AAGO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;IACrD,OAAO,IAAI,UAAU,GAAG;QACtB,OAAO,GAAG,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC3C,OAAO;QACL,OAAO,GAAG,iBAAiB,CAAC,CAAC;IAC/B;AACF;AAGO,SAAS,kBAAkB,OAAe;IAC/C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;IACzC,OAAO;QACL,OAAO,GAAG,iBAAiB,CAAC,CAAC;IAC/B;AACF;AAGO,SAAS,oBAAoB,KAAa,EAAE,QAAgB;IACjE,IAAI,aAAa,GAAG,OAAO;IAC3B,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,WAAY;AACzC;AAGO,SAAS,oBAAoB,UAAkB;IAKpD,IAAI,cAAc,IAAI;QACpB,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO;QACL,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF;AACF;AAGO,SAAS,iBAAiB,QAAuB;IACtD,OAAQ;QACN,KAAK,qHAAA,CAAA,gBAAa,CAAC,YAAY;YAC7B,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,cAAc;YAC/B,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,gBAAgB;YACjC,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,mBAAmB;YACpC,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,oBAAoB;YACrC,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,mBAAmB,UAAsB;IACvD,OAAQ;QACN,KAAK,qHAAA,CAAA,aAAU,CAAC,QAAQ;YACtB,OAAO;QACT,KAAK,qHAAA,CAAA,aAAU,CAAC,aAAa;YAC3B,OAAO;QACT,KAAK,qHAAA,CAAA,aAAU,CAAC,MAAM;YACpB,OAAO;QACT,KAAK,qHAAA,CAAA,aAAU,CAAC,MAAM;YACpB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,kBAAkB,QAAuB;IACvD,OAAQ;QACN,KAAK,qHAAA,CAAA,gBAAa,CAAC,YAAY;YAC7B,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,cAAc;YAC/B,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,gBAAgB;YACjC,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,mBAAmB;YACpC,OAAO;QACT,KAAK,qHAAA,CAAA,gBAAa,CAAC,oBAAoB;YACrC,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,oBAAoB,UAAsB;IACxD,OAAQ;QACN,KAAK,qHAAA,CAAA,aAAU,CAAC,QAAQ;YACtB,OAAO;QACT,KAAK,qHAAA,CAAA,aAAU,CAAC,aAAa;YAC3B,OAAO;QACT,KAAK,qHAAA,CAAA,aAAU,CAAC,MAAM;YACpB,OAAO;QACT,KAAK,qHAAA,CAAA,aAAU,CAAC,MAAM;YACpB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,sBACd,UAAkB,EAClB,aAAqB,EACrB,YAAoB,CAAC;IAErB,MAAM,UAAU,WAAW,WAAW,OAAO,CAAC,YAAY;IAC1D,MAAM,aAAa,WAAW,cAAc,OAAO,CAAC,YAAY;IAEhE,IAAI,MAAM,YAAY,MAAM,aAAa;QACvC,OAAO;IACT;IAEA,IAAI,cAAc,GAAG;QACnB,OAAO,YAAY;IACrB;IAEA,MAAM,OAAO,KAAK,GAAG,CAAC,UAAU;IAChC,MAAM,iBAAiB,KAAK,GAAG,CAAC,aAAa,YAAY;IAEzD,OAAO,QAAQ;AACjB;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAGO,SAAS,wBACd,YAAoB,EACpB,KAAa,EACb,QAAgB,EAChB,eAAyB;IAEzB,MAAM,aAAa,oBAAoB,OAAO;IAE9C,qEAAqE;IACrE,IAAI,cAAc,IAAI;QACpB,2EAA2E;QAC3E,MAAM,oBAAoB,eAAe;QACzC,IAAI,qBAAqB,OAAO,CAAC,gBAAgB,QAAQ,CAAC,oBAAoB;YAC5E,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,MAAM,YAAY,eAAe;IACjC,IAAI,aAAa,OAAO,CAAC,gBAAgB,QAAQ,CAAC,YAAY;QAC5D,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,uBAAuB,eAAyB,EAAE,cAAsB,GAAG;IACzF,MAAM,YAAY,gBAAgB,MAAM;IACxC,MAAM,aAAa,AAAC,YAAY,cAAe;IAE/C,OAAO;QACL;QACA,OAAO;QACP,YAAY,KAAK,KAAK,CAAC;QACvB,WAAW,cAAc;IAC3B;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAGO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/CG/master-cg/src/app/levels/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { \n  Search, \n  Filter, \n  Play, \n  Lock, \n  CheckCircle, \n  Clock, \n  Star,\n  BookOpen,\n  Target\n} from \"lucide-react\";\nimport { LevelCategory, Difficulty } from \"@/types\";\nimport { \n  translateCategory, \n  translateDifficulty, \n  getCategoryColor, \n  getDifficultyColor \n} from \"@/lib/utils\";\n\n// Données mockées pour la démonstration\nconst mockLevels = Array.from({ length: 50 }, (_, i) => {\n  const id = i + 1;\n  const categories = Object.values(LevelCategory);\n  const difficulties = Object.values(Difficulty);\n  \n  return {\n    id,\n    title: `Niveau ${id} - ${getRandomTitle(id)}`,\n    description: `Description du niveau ${id}`,\n    category: categories[Math.floor(i / 10) % categories.length],\n    difficulty: difficulties[Math.floor(i / 12) % difficulties.length],\n    estimatedTime: 15 + (i % 4) * 10,\n    maxScore: 100 + (i % 3) * 50,\n    isCompleted: i < 14,\n    isUnlocked: i < 15,\n    userScore: i < 14 ? 75 + Math.floor(Math.random() * 25) : null,\n    prerequisites: i === 0 ? [] : [i]\n  };\n});\n\nfunction getRandomTitle(id: number): string {\n  const titles = [\n    \"Calcul du coût unitaire\",\n    \"Seuil de rentabilité\",\n    \"Analyse des écarts\",\n    \"Budget des ventes\",\n    \"Marge sur coût variable\",\n    \"Coûts préétablis\",\n    \"Tableau de bord\",\n    \"Ratios financiers\",\n    \"VAN et TRI\",\n    \"Pricing stratégique\"\n  ];\n  return titles[id % titles.length];\n}\n\nexport default function LevelsPage() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState<LevelCategory | \"ALL\">(\"ALL\");\n  const [selectedDifficulty, setSelectedDifficulty] = useState<Difficulty | \"ALL\">(\"ALL\");\n\n  const filteredLevels = mockLevels.filter(level => {\n    const matchesSearch = level.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         level.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === \"ALL\" || level.category === selectedCategory;\n    const matchesDifficulty = selectedDifficulty === \"ALL\" || level.difficulty === selectedDifficulty;\n    \n    return matchesSearch && matchesCategory && matchesDifficulty;\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n                Master CG\n              </Link>\n              <nav className=\"hidden md:flex space-x-6\">\n                <Link href=\"/dashboard\" className=\"text-gray-600 hover:text-gray-900\">Dashboard</Link>\n                <Link href=\"/levels\" className=\"text-blue-600 font-medium\">Niveaux</Link>\n                <Link href=\"/progress\" className=\"text-gray-600 hover:text-gray-900\">Progression</Link>\n                <Link href=\"/achievements\" className=\"text-gray-600 hover:text-gray-900\">Succès</Link>\n              </nav>\n            </div>\n            <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-700\">\n              ← Retour au dashboard\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Tous les niveaux\n          </h1>\n          <p className=\"text-gray-600\">\n            Explorez les 300 niveaux de Master CG et progressez à votre rythme\n          </p>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg p-6 shadow-sm mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {/* Search */}\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher un niveau...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            {/* Category Filter */}\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value as LevelCategory | \"ALL\")}\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"ALL\">Toutes les catégories</option>\n              {Object.values(LevelCategory).map(category => (\n                <option key={category} value={category}>\n                  {translateCategory(category)}\n                </option>\n              ))}\n            </select>\n\n            {/* Difficulty Filter */}\n            <select\n              value={selectedDifficulty}\n              onChange={(e) => setSelectedDifficulty(e.target.value as Difficulty | \"ALL\")}\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"ALL\">Toutes les difficultés</option>\n              {Object.values(Difficulty).map(difficulty => (\n                <option key={difficulty} value={difficulty}>\n                  {translateDifficulty(difficulty)}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {filteredLevels.length} niveau{filteredLevels.length > 1 ? 's' : ''} trouvé{filteredLevels.length > 1 ? 's' : ''}\n          </p>\n        </div>\n\n        {/* Levels Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredLevels.map((level) => (\n            <div\n              key={level.id}\n              className={`bg-white rounded-lg p-6 shadow-sm border-2 transition-all duration-200 ${\n                level.isUnlocked \n                  ? 'border-transparent hover:border-blue-300 hover:shadow-md' \n                  : 'border-gray-200 opacity-60'\n              }`}\n            >\n              {/* Level Header */}\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <span className=\"text-sm font-medium text-gray-500\">Niveau {level.id}</span>\n                    {level.isCompleted && (\n                      <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    )}\n                    {!level.isUnlocked && (\n                      <Lock className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                    {level.title}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 mb-4\">\n                    {level.description}\n                  </p>\n                </div>\n              </div>\n\n              {/* Level Info */}\n              <div className=\"space-y-3 mb-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className={`px-2 py-1 rounded text-xs font-medium ${getCategoryColor(level.category)}`}>\n                    {translateCategory(level.category)}\n                  </span>\n                  <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(level.difficulty)}`}>\n                    {translateDifficulty(level.difficulty)}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                  <div className=\"flex items-center space-x-1\">\n                    <Clock className=\"h-4 w-4\" />\n                    <span>{level.estimatedTime} min</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <Target className=\"h-4 w-4\" />\n                    <span>{level.maxScore} pts max</span>\n                  </div>\n                </div>\n\n                {level.isCompleted && level.userScore && (\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Votre score:</span>\n                    <div className=\"flex items-center space-x-1\">\n                      <Star className=\"h-4 w-4 text-yellow-500\" />\n                      <span className=\"font-medium text-gray-900\">\n                        {level.userScore}/{level.maxScore}\n                      </span>\n                      <span className=\"text-gray-600\">\n                        ({Math.round((level.userScore / level.maxScore) * 100)}%)\n                      </span>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Action Button */}\n              <div className=\"pt-4 border-t border-gray-100\">\n                {level.isUnlocked ? (\n                  <Link\n                    href={`/level/${level.id}`}\n                    className=\"w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\"\n                  >\n                    {level.isCompleted ? (\n                      <>\n                        <BookOpen className=\"mr-2 h-4 w-4\" />\n                        Revoir\n                      </>\n                    ) : (\n                      <>\n                        <Play className=\"mr-2 h-4 w-4\" />\n                        Commencer\n                      </>\n                    )}\n                  </Link>\n                ) : (\n                  <div className=\"w-full inline-flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-500 rounded-lg font-medium cursor-not-allowed\">\n                    <Lock className=\"mr-2 h-4 w-4\" />\n                    Verrouillé\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Load More Button (pour la pagination future) */}\n        {filteredLevels.length >= 50 && (\n          <div className=\"text-center mt-12\">\n            <button className=\"px-8 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors\">\n              Charger plus de niveaux\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAhBA;;;;;;;AAuBA,wCAAwC;AACxC,MAAM,aAAa,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,CAAC,GAAG;IAChD,MAAM,KAAK,IAAI;IACf,MAAM,aAAa,OAAO,MAAM,CAAC,qHAAA,CAAA,gBAAa;IAC9C,MAAM,eAAe,OAAO,MAAM,CAAC,qHAAA,CAAA,aAAU;IAE7C,OAAO;QACL;QACA,OAAO,CAAC,OAAO,EAAE,GAAG,GAAG,EAAE,eAAe,KAAK;QAC7C,aAAa,CAAC,sBAAsB,EAAE,IAAI;QAC1C,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,WAAW,MAAM,CAAC;QAC5D,YAAY,YAAY,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,aAAa,MAAM,CAAC;QAClE,eAAe,KAAK,AAAC,IAAI,IAAK;QAC9B,UAAU,MAAM,AAAC,IAAI,IAAK;QAC1B,aAAa,IAAI;QACjB,YAAY,IAAI;QAChB,WAAW,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;QAC1D,eAAe,MAAM,IAAI,EAAE,GAAG;YAAC;SAAE;IACnC;AACF;AAEA,SAAS,eAAe,EAAU;IAChC,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC;AACnC;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEjF,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,MAAM,kBAAkB,qBAAqB,SAAS,MAAM,QAAQ,KAAK;QACzE,MAAM,oBAAoB,uBAAuB,SAAS,MAAM,UAAU,KAAK;QAE/E,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAoC;;;;;;0DACtE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA4B;;;;;;0DAC3D,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAoC;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAoC;;;;;;;;;;;;;;;;;;0CAG7E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACnD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;wCACnB,OAAO,MAAM,CAAC,qHAAA,CAAA,gBAAa,EAAE,GAAG,CAAC,CAAA,yBAChC,8OAAC;gDAAsB,OAAO;0DAC3B,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE;+CADR;;;;;;;;;;;8CAOjB,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;oCACrD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;wCACnB,OAAO,MAAM,CAAC,qHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAA,2BAC7B,8OAAC;gDAAwB,OAAO;0DAC7B,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;+CADV;;;;;;;;;;;;;;;;;;;;;;kCASrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCACV,eAAe,MAAM;gCAAC;gCAAQ,eAAe,MAAM,GAAG,IAAI,MAAM;gCAAG;gCAAQ,eAAe,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;kCAKlH,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;gCAEC,WAAW,CAAC,uEAAuE,EACjF,MAAM,UAAU,GACZ,6DACA,8BACJ;;kDAGF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAoC;gEAAQ,MAAM,EAAE;;;;;;;wDACnE,MAAM,WAAW,kBAChB,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAExB,CAAC,MAAM,UAAU,kBAChB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;8DAGpB,8OAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;;;;;;;;;;;;kDAMxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,sCAAsC,EAAE,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,QAAQ,GAAG;kEACzF,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,QAAQ;;;;;;kEAEnC,8OAAC;wDAAK,WAAW,CAAC,sCAAsC,EAAE,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,UAAU,GAAG;kEAC7F,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,UAAU;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;;oEAAM,MAAM,aAAa;oEAAC;;;;;;;;;;;;;kEAE7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;;oEAAM,MAAM,QAAQ;oEAAC;;;;;;;;;;;;;;;;;;;4CAIzB,MAAM,WAAW,IAAI,MAAM,SAAS,kBACnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;;oEACb,MAAM,SAAS;oEAAC;oEAAE,MAAM,QAAQ;;;;;;;0EAEnC,8OAAC;gEAAK,WAAU;;oEAAgB;oEAC5B,KAAK,KAAK,CAAC,AAAC,MAAM,SAAS,GAAG,MAAM,QAAQ,GAAI;oEAAK;;;;;;;;;;;;;;;;;;;;;;;;;kDAQjE,8OAAC;wCAAI,WAAU;kDACZ,MAAM,UAAU,iBACf,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;4CAC1B,WAAU;sDAET,MAAM,WAAW,iBAChB;;kEACE,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;6EAIvC;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;iEAMvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;+BAvFlC,MAAM,EAAE;;;;;;;;;;oBAiGlB,eAAe,MAAM,IAAI,oBACxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;sCAAoH;;;;;;;;;;;;;;;;;;;;;;;AAQlJ", "debugId": null}}]}