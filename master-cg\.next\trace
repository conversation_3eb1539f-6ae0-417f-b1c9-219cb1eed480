[{"name": "hot-reloader", "duration": 353, "timestamp": 177585041531, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752143764333, "traceId": "d200cb421d6648a8"}, {"name": "setup-dev-bundler", "duration": 1873289, "timestamp": 177584230063, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752143763521, "traceId": "d200cb421d6648a8"}, {"name": "run-instrumentation-hook", "duration": 67, "timestamp": 177586360251, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752143765652, "traceId": "d200cb421d6648a8"}, {"name": "start-dev-server", "duration": 5211846, "timestamp": 177581171711, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "3600355328", "memory.totalMem": "17015463936", "memory.heapSizeLimit": "8557428736", "memory.rss": "178012160", "memory.heapTotal": "97173504", "memory.heapUsed": "75653672"}, "startTime": 1752143760464, "traceId": "d200cb421d6648a8"}, {"name": "compile-path", "duration": 15708954, "timestamp": 177638494009, "id": 7, "tags": {"trigger": "/"}, "startTime": 1752143817785, "traceId": "d200cb421d6648a8"}, {"name": "ensure-page", "duration": 15715127, "timestamp": 177638491760, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1752143817783, "traceId": "d200cb421d6648a8"}]