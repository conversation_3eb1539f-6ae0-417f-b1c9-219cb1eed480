"use client";

import { useState } from "react";
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Lightbulb, 
  ChevronDown, 
  ChevronUp,
  Star,
  AlertCircle,
  CheckCircle
} from "lucide-react";

interface AIFeedbackProps {
  feedback: {
    overallFeedback: string;
    strengths: string[];
    improvements: string[];
    recommendations: string[];
    nextLevelSuggestion?: number;
  };
  score: number;
  maxScore: number;
  levelTitle: string;
}

export default function AIFeedback({ 
  feedback, 
  score, 
  maxScore, 
  levelTitle 
}: AIFeedbackProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const percentage = Math.round((score / maxScore) * 100);

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-600";
    if (percentage >= 80) return "text-blue-600";
    if (percentage >= 70) return "text-yellow-600";
    if (percentage >= 60) return "text-orange-600";
    return "text-red-600";
  };

  const getPerformanceIcon = (percentage: number) => {
    if (percentage >= 90) return <Star className="h-5 w-5 text-green-600" />;
    if (percentage >= 80) return <CheckCircle className="h-5 w-5 text-blue-600" />;
    if (percentage >= 70) return <Target className="h-5 w-5 text-yellow-600" />;
    return <AlertCircle className="h-5 w-5 text-orange-600" />;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header */}
      <div 
        className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Brain className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Feedback IA Personnalisé
              </h3>
              <p className="text-sm text-gray-600">
                Analyse intelligente de votre performance
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {getPerformanceIcon(percentage)}
              <span className={`text-lg font-semibold ${getPerformanceColor(percentage)}`}>
                {percentage}%
              </span>
            </div>
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="p-6 space-y-6">
          {/* Overall Feedback */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Brain className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900 mb-2">
                  Analyse générale
                </h4>
                <p className="text-blue-800 leading-relaxed">
                  {feedback.overallFeedback}
                </p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Strengths */}
            {feedback.strengths.length > 0 && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <h4 className="font-medium text-green-900">
                    Points forts
                  </h4>
                </div>
                <ul className="space-y-2">
                  {feedback.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-green-800 text-sm">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Improvements */}
            {feedback.improvements.length > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <TrendingUp className="h-5 w-5 text-orange-600" />
                  <h4 className="font-medium text-orange-900">
                    Axes d'amélioration
                  </h4>
                </div>
                <ul className="space-y-2">
                  {feedback.improvements.map((improvement, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-orange-800 text-sm">{improvement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Recommendations */}
          {feedback.recommendations.length > 0 && (
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Lightbulb className="h-5 w-5 text-purple-600" />
                <h4 className="font-medium text-purple-900">
                  Recommandations personnalisées
                </h4>
              </div>
              <ul className="space-y-3">
                {feedback.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-purple-600">
                        {index + 1}
                      </span>
                    </div>
                    <span className="text-purple-800 text-sm leading-relaxed">
                      {recommendation}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Next Level Suggestion */}
          {feedback.nextLevelSuggestion && (
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <Target className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-indigo-900">
                      Niveau recommandé
                    </h4>
                    <p className="text-sm text-indigo-700">
                      Basé sur votre performance actuelle
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-indigo-600">
                    #{feedback.nextLevelSuggestion}
                  </div>
                  <button className="text-sm text-indigo-600 hover:text-indigo-700 font-medium">
                    Commencer →
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Performance Metrics */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">
              Métriques de performance
            </h4>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {score}
                </div>
                <div className="text-sm text-gray-600">Points obtenus</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {maxScore}
                </div>
                <div className="text-sm text-gray-600">Points maximum</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${getPerformanceColor(percentage)}`}>
                  {percentage}%
                </div>
                <div className="text-sm text-gray-600">Réussite</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
            <button className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors">
              Sauvegarder le feedback
            </button>
            <button className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              Partager les résultats
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
