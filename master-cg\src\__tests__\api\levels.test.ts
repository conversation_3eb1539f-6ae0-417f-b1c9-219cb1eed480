/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http';
import { GET } from '@/app/api/levels/[id]/route';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    level: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock sample levels
jest.mock('@/data/sample-levels', () => ({
  SAMPLE_LEVELS: [
    {
      id: 1,
      title: 'Test Level',
      description: 'Test Description',
      category: 'FONDAMENTAUX',
      difficulty: 'DEBUTANT',
      estimatedTime: 15,
      prerequisites: [],
      scenario: 'Test scenario',
      context: { company: 'Test Company' },
      questions: [],
      maxScore: 100,
      hints: [],
      resources: []
    }
  ],
  generateBasicLevel: jest.fn()
}));

import { prisma } from '@/lib/prisma';

describe('/api/levels/[id]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return level from database if exists', async () => {
      const mockLevel = {
        id: 1,
        title: 'Existing Level',
        description: 'From database',
        category: 'FONDAMENTAUX',
        difficulty: 'DEBUTANT',
        estimatedTime: 15,
        prerequisites: [],
        scenario: 'Database scenario',
        context: { company: 'DB Company' },
        questions: [],
        maxScore: 100,
        hints: [],
        resources: []
      };

      (prisma.level.findUnique as jest.Mock).mockResolvedValue(mockLevel);

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req, { params: { id: '1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockLevel);
      expect(prisma.level.findUnique).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });

    it('should create and return sample level if not in database', async () => {
      const mockCreatedLevel = {
        id: 1,
        title: 'Test Level',
        description: 'Test Description',
        category: 'FONDAMENTAUX',
        difficulty: 'DEBUTANT',
        estimatedTime: 15,
        prerequisites: [],
        scenario: 'Test scenario',
        context: { company: 'Test Company' },
        questions: [],
        maxScore: 100,
        hints: [],
        resources: []
      };

      (prisma.level.findUnique as jest.Mock).mockResolvedValue(null);
      (prisma.level.create as jest.Mock).mockResolvedValue(mockCreatedLevel);

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req, { params: { id: '1' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockCreatedLevel);
      expect(prisma.level.create).toHaveBeenCalled();
    });

    it('should return 400 for invalid level ID', async () => {
      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req, { params: { id: 'invalid' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('ID de niveau invalide');
    });

    it('should return 400 for level ID out of range', async () => {
      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req, { params: { id: '301' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('ID de niveau invalide');
    });

    it('should handle database errors gracefully', async () => {
      (prisma.level.findUnique as jest.Mock).mockRejectedValue(new Error('Database error'));

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req, { params: { id: '1' } });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Erreur serveur lors de la récupération du niveau');
    });
  });
});
