import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { GameEngine } from '@/lib/game-engine';
import { generateFeedback } from '@/lib/openai';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const levelId = parseInt(params.id);
    const body = await request.json();
    
    const {
      userId = 'demo-user', // Pour la démo, on utilise un utilisateur par défaut
      answers,
      timeSpent,
      hintsUsed = 0
    } = body;

    if (isNaN(levelId) || levelId < 1 || levelId > 300) {
      return NextResponse.json(
        { error: 'ID de niveau invalide' },
        { status: 400 }
      );
    }

    // Récupérer le niveau
    const level = await prisma.level.findUnique({
      where: { id: levelId }
    });

    if (!level) {
      return NextResponse.json(
        { error: 'Niveau non trouvé' },
        { status: 404 }
      );
    }

    // Calculer le score avec le moteur de jeu
    const scoreResult = GameEngine.calculateScore(
      level.questions as any[],
      answers,
      hintsUsed
    );

    // Créer la tentative en base
    const attempt = await prisma.levelAttempt.create({
      data: {
        userId,
        levelId,
        answers: scoreResult.questionResults,
        totalScore: scoreResult.totalScore,
        maxScore: scoreResult.maxScore,
        percentage: scoreResult.percentage,
        timeSpent,
        hintsUsed,
        completed: true,
        completedAt: new Date()
      }
    });

    // Mettre à jour ou créer le progrès utilisateur
    let userProgress = await prisma.userProgress.findUnique({
      where: { userId }
    });

    if (!userProgress) {
      // Créer un utilisateur de démo s'il n'existe pas
      await prisma.user.upsert({
        where: { id: userId },
        update: {},
        create: {
          id: userId,
          email: '<EMAIL>',
          name: 'Utilisateur Démo'
        }
      });

      userProgress = await prisma.userProgress.create({
        data: {
          userId,
          currentLevel: levelId,
          totalScore: scoreResult.totalScore,
          completedLevels: [levelId],
          unlockedLevels: [levelId, levelId + 1].filter(l => l <= 300),
          lastPlayedAt: new Date()
        }
      });
    } else {
      // Mettre à jour le progrès existant
      const newCompletedLevels = userProgress.completedLevels.includes(levelId)
        ? userProgress.completedLevels
        : [...userProgress.completedLevels, levelId];
      
      const newUnlockedLevels = [...new Set([
        ...userProgress.unlockedLevels,
        levelId + 1,
        levelId + 2 // Débloquer le niveau suivant et celui d'après
      ])].filter(l => l <= 300);

      userProgress = await prisma.userProgress.update({
        where: { userId },
        data: {
          currentLevel: Math.max(userProgress.currentLevel, levelId + 1),
          totalScore: userProgress.totalScore + scoreResult.totalScore,
          completedLevels: newCompletedLevels,
          unlockedLevels: newUnlockedLevels,
          lastPlayedAt: new Date(),
          totalTimeSpent: userProgress.totalTimeSpent + Math.round(timeSpent / 60)
        }
      });
    }

    // Générer le feedback IA (optionnel, peut être fait en arrière-plan)
    let aiFeedback = null;
    try {
      const feedbackData = await generateFeedback(
        level.title,
        scoreResult.questionResults,
        level.questions as any[],
        scoreResult.totalScore,
        scoreResult.maxScore
      );

      aiFeedback = await prisma.aIFeedback.create({
        data: {
          attemptId: attempt.id,
          userId,
          overallFeedback: feedbackData.overallFeedback,
          strengths: feedbackData.strengths,
          improvements: feedbackData.improvements,
          recommendations: feedbackData.recommendations,
          nextLevelSuggestion: feedbackData.nextLevelSuggestion
        }
      });
    } catch (error) {
      console.error('Erreur lors de la génération du feedback IA:', error);
      // Le feedback IA n'est pas critique, on continue sans
    }

    // Vérifier les achievements débloqués
    const unlockedAchievements = GameEngine.checkAchievements(
      attempt,
      userProgress,
      [] // TODO: récupérer toutes les tentatives de l'utilisateur
    );

    // Enregistrer les nouveaux achievements
    for (const achievementType of unlockedAchievements) {
      try {
        const achievement = await prisma.achievement.findFirst({
          where: { 
            name: achievementType,
            isActive: true
          }
        });

        if (achievement) {
          await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id
            }
          });
        }
      } catch (error) {
        // Achievement déjà débloqué ou autre erreur, on continue
        console.log(`Achievement ${achievementType} déjà débloqué ou erreur`);
      }
    }

    // Enregistrer l'activité
    await prisma.activityLog.create({
      data: {
        userId,
        type: 'LEVEL_COMPLETED',
        description: `Niveau ${levelId} terminé avec ${scoreResult.percentage}%`,
        metadata: {
          levelId,
          score: scoreResult.totalScore,
          maxScore: scoreResult.maxScore,
          percentage: scoreResult.percentage
        },
        points: scoreResult.totalScore
      }
    });

    // Retourner les résultats
    return NextResponse.json({
      success: true,
      data: {
        attempt: {
          id: attempt.id,
          totalScore: scoreResult.totalScore,
          maxScore: scoreResult.maxScore,
          percentage: scoreResult.percentage,
          timeSpent,
          hintsUsed,
          questionResults: scoreResult.questionResults
        },
        userProgress: {
          currentLevel: userProgress.currentLevel,
          totalScore: userProgress.totalScore,
          completedLevels: userProgress.completedLevels,
          unlockedLevels: userProgress.unlockedLevels
        },
        aiFeedback: aiFeedback ? {
          overallFeedback: aiFeedback.overallFeedback,
          strengths: aiFeedback.strengths,
          improvements: aiFeedback.improvements,
          recommendations: aiFeedback.recommendations,
          nextLevelSuggestion: aiFeedback.nextLevelSuggestion
        } : null,
        unlockedAchievements,
        nextRecommendedLevel: GameEngine.getNextRecommendedLevel(
          levelId,
          scoreResult.totalScore,
          scoreResult.maxScore,
          userProgress.completedLevels
        )
      }
    });

  } catch (error) {
    console.error('Erreur lors de la soumission de la tentative:', error);
    return NextResponse.json(
      { error: 'Erreur serveur lors de la soumission de la tentative' },
      { status: 500 }
    );
  }
}
