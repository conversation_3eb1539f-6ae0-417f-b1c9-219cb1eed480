import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { SAMPLE_LEVELS, generateBasicLevel } from '@/data/sample-levels';
import { 
  getCategoryForLevel, 
  getDifficultyForLevel, 
  getEstimatedTimeForLevel, 
  getMaxScoreForLevel, 
  getPrerequisitesForLevel,
  getThemeForLevel,
  getTopicsForLevel 
} from '@/data/levels-structure';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const levelId = parseInt(params.id);
    
    if (isNaN(levelId) || levelId < 1 || levelId > 300) {
      return NextResponse.json(
        { error: 'ID de niveau invalide' },
        { status: 400 }
      );
    }

    // Essayer de récupérer le niveau depuis la base de données
    let level = await prisma.level.findUnique({
      where: { id: levelId }
    });

    // Si le niveau n'existe pas en base, le générer
    if (!level) {
      const sampleLevel = SAMPLE_LEVELS.find(l => l.id === levelId);
      
      if (sampleLevel) {
        // Utiliser le niveau d'exemple détaillé
        level = await prisma.level.create({
          data: {
            id: levelId,
            title: sampleLevel.title!,
            description: sampleLevel.description!,
            category: sampleLevel.category!,
            difficulty: sampleLevel.difficulty!,
            estimatedTime: sampleLevel.estimatedTime!,
            prerequisites: sampleLevel.prerequisites!,
            scenario: sampleLevel.scenario!,
            context: sampleLevel.context!,
            questions: sampleLevel.questions!,
            maxScore: sampleLevel.maxScore!,
            hints: sampleLevel.hints!,
            resources: sampleLevel.resources || []
          }
        });
      } else {
        // Générer un niveau basique
        const category = getCategoryForLevel(levelId);
        const difficulty = getDifficultyForLevel(levelId);
        const theme = getThemeForLevel(levelId);
        const topics = getTopicsForLevel(levelId);
        
        const generatedLevel = generateBasicLevel(levelId, `${theme} - Niveau ${levelId}`, category, difficulty);
        
        level = await prisma.level.create({
          data: {
            id: levelId,
            title: generatedLevel.title!,
            description: generatedLevel.description!,
            category: generatedLevel.category!,
            difficulty: generatedLevel.difficulty!,
            estimatedTime: generatedLevel.estimatedTime!,
            prerequisites: generatedLevel.prerequisites!,
            scenario: generatedLevel.scenario!,
            context: generatedLevel.context!,
            questions: generatedLevel.questions!,
            maxScore: generatedLevel.maxScore!,
            hints: generatedLevel.hints!,
            resources: generatedLevel.resources || []
          }
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: level
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du niveau:', error);
    return NextResponse.json(
      { error: 'Erreur serveur lors de la récupération du niveau' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const levelId = parseInt(params.id);
    const body = await request.json();
    
    if (isNaN(levelId) || levelId < 1 || levelId > 300) {
      return NextResponse.json(
        { error: 'ID de niveau invalide' },
        { status: 400 }
      );
    }

    const updatedLevel = await prisma.level.update({
      where: { id: levelId },
      data: {
        title: body.title,
        description: body.description,
        scenario: body.scenario,
        context: body.context,
        questions: body.questions,
        hints: body.hints,
        resources: body.resources,
        isActive: body.isActive
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedLevel
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du niveau:', error);
    return NextResponse.json(
      { error: 'Erreur serveur lors de la mise à jour du niveau' },
      { status: 500 }
    );
  }
}
