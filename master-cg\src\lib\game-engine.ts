import { QuestionType, UserAnswer, Level, LevelAttempt } from "@/types";
import { validateNumericAnswer } from "./utils";

export class GameEngine {
  /**
   * Valide une réponse utilisateur pour une question donnée
   */
  static validateAnswer(question: any, userAnswer: string): boolean {
    if (!userAnswer || userAnswer.trim() === "") {
      return false;
    }

    switch (question.type) {
      case QuestionType.QCM:
        return userAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
      
      case QuestionType.QCM_MULTIPLE:
        const userAnswers = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
        const correctAnswers = Array.isArray(question.correctAnswer) 
          ? question.correctAnswer 
          : [question.correctAnswer];
        
        return userAnswers.length === correctAnswers.length &&
               userAnswers.every(answer => correctAnswers.includes(answer));
      
      case QuestionType.NUMERIQUE:
        return validateNumericAnswer(
          userAnswer, 
          question.correctAnswer, 
          question.tolerance || 0
        );
      
      case QuestionType.TEXTE_LIBRE:
        // Pour les réponses texte libre, on peut implémenter une logique plus sophistiquée
        return userAnswer.toLowerCase().trim().includes(
          question.correctAnswer.toLowerCase().trim()
        );
      
      case QuestionType.CALCUL:
        return validateNumericAnswer(
          userAnswer, 
          question.correctAnswer, 
          question.tolerance || 1 // Tolérance de 1% par défaut pour les calculs
        );
      
      default:
        return false;
    }
  }

  /**
   * Calcule le score pour une tentative de niveau
   */
  static calculateScore(
    questions: any[], 
    userAnswers: Record<string, string>,
    hintsUsed: number = 0
  ): {
    totalScore: number;
    maxScore: number;
    percentage: number;
    questionResults: UserAnswer[];
  } {
    let totalScore = 0;
    const maxScore = questions.reduce((sum, q) => sum + q.points, 0);
    
    const questionResults: UserAnswer[] = questions.map(question => {
      const userAnswer = userAnswers[question.id] || "";
      const isCorrect = this.validateAnswer(question, userAnswer);
      
      // Calcul des points avec pénalité pour les indices
      let pointsEarned = isCorrect ? question.points : 0;
      
      // Réduction de 10% des points par indice utilisé (maximum 50% de réduction)
      const hintPenalty = Math.min(hintsUsed * 0.1, 0.5);
      pointsEarned = Math.round(pointsEarned * (1 - hintPenalty));
      
      totalScore += pointsEarned;
      
      return {
        questionId: question.id,
        answer: userAnswer,
        isCorrect,
        pointsEarned,
        timeSpent: 0, // À implémenter si nécessaire
        hintsUsed: 0  // À implémenter par question si nécessaire
      };
    });

    const percentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;

    return {
      totalScore,
      maxScore,
      percentage,
      questionResults
    };
  }

  /**
   * Détermine si un niveau est débloqué pour un utilisateur
   */
  static isLevelUnlocked(
    levelId: number, 
    completedLevels: number[], 
    prerequisites: number[]
  ): boolean {
    if (levelId === 1) return true; // Le premier niveau est toujours débloqué
    
    // Vérifier que tous les prérequis sont complétés
    return prerequisites.every(prereq => completedLevels.includes(prereq));
  }

  /**
   * Calcule le niveau suivant recommandé
   */
  static getNextRecommendedLevel(
    currentLevel: number,
    score: number,
    maxScore: number,
    completedLevels: number[],
    userPreferences?: {
      preferredCategory?: string;
      skipDifficultLevels?: boolean;
    }
  ): number | null {
    const percentage = (score / maxScore) * 100;
    
    // Si le score est excellent (>= 85%), suggérer un saut de niveau
    if (percentage >= 85 && userPreferences?.skipDifficultLevels !== false) {
      const jumpLevel = currentLevel + 2;
      if (jumpLevel <= 300 && !completedLevels.includes(jumpLevel)) {
        return jumpLevel;
      }
    }
    
    // Sinon, niveau suivant logique
    const nextLevel = currentLevel + 1;
    if (nextLevel <= 300 && !completedLevels.includes(nextLevel)) {
      return nextLevel;
    }
    
    return null;
  }

  /**
   * Génère des statistiques de performance pour un utilisateur
   */
  static generatePerformanceStats(attempts: LevelAttempt[]) {
    if (attempts.length === 0) {
      return {
        averageScore: 0,
        averageTime: 0,
        totalAttempts: 0,
        completionRate: 0,
        strongCategories: [],
        weakCategories: [],
        improvementTrend: 0
      };
    }

    const completedAttempts = attempts.filter(a => a.completed);
    const totalScore = completedAttempts.reduce((sum, a) => sum + a.totalScore, 0);
    const totalMaxScore = completedAttempts.reduce((sum, a) => sum + a.maxScore, 0);
    const totalTime = completedAttempts.reduce((sum, a) => sum + a.timeSpent, 0);

    const averageScore = totalMaxScore > 0 ? (totalScore / totalMaxScore) * 100 : 0;
    const averageTime = completedAttempts.length > 0 ? totalTime / completedAttempts.length : 0;
    const completionRate = attempts.length > 0 ? (completedAttempts.length / attempts.length) * 100 : 0;

    // Calcul de la tendance d'amélioration (comparaison des 5 derniers vs 5 premiers)
    const recentAttempts = completedAttempts.slice(-5);
    const oldAttempts = completedAttempts.slice(0, 5);
    
    const recentAvg = recentAttempts.length > 0 
      ? recentAttempts.reduce((sum, a) => sum + (a.totalScore / a.maxScore) * 100, 0) / recentAttempts.length
      : 0;
    const oldAvg = oldAttempts.length > 0 
      ? oldAttempts.reduce((sum, a) => sum + (a.totalScore / a.maxScore) * 100, 0) / oldAttempts.length
      : 0;
    
    const improvementTrend = recentAvg - oldAvg;

    return {
      averageScore: Math.round(averageScore),
      averageTime: Math.round(averageTime / 60), // en minutes
      totalAttempts: attempts.length,
      completionRate: Math.round(completionRate),
      strongCategories: [], // À implémenter avec les données de catégories
      weakCategories: [], // À implémenter avec les données de catégories
      improvementTrend: Math.round(improvementTrend)
    };
  }

  /**
   * Détermine les achievements débloqués après une tentative
   */
  static checkAchievements(
    attempt: LevelAttempt,
    userProgress: any,
    allAttempts: LevelAttempt[]
  ): string[] {
    const unlockedAchievements: string[] = [];
    
    // Premier niveau complété
    if (userProgress.completedLevels.length === 1) {
      unlockedAchievements.push("FIRST_LEVEL");
    }
    
    // Score parfait
    if (attempt.percentage === 100) {
      unlockedAchievements.push("PERFECT_SCORE");
    }
    
    // Série de niveaux
    if (userProgress.completedLevels.length === 5) {
      unlockedAchievements.push("FIVE_LEVELS");
    }
    
    if (userProgress.completedLevels.length === 10) {
      unlockedAchievements.push("TEN_LEVELS");
    }
    
    if (userProgress.completedLevels.length === 50) {
      unlockedAchievements.push("FIFTY_LEVELS");
    }
    
    // Série de jours consécutifs
    if (userProgress.streakDays === 7) {
      unlockedAchievements.push("WEEK_STREAK");
    }
    
    if (userProgress.streakDays === 30) {
      unlockedAchievements.push("MONTH_STREAK");
    }
    
    // Vitesse d'exécution
    const estimatedTime = 20 * 60; // 20 minutes en secondes
    if (attempt.timeSpent < estimatedTime * 0.5) {
      unlockedAchievements.push("SPEED_DEMON");
    }
    
    // Pas d'indices utilisés
    if (attempt.hintsUsed === 0 && attempt.percentage >= 80) {
      unlockedAchievements.push("NO_HINTS_MASTER");
    }
    
    return unlockedAchievements;
  }

  /**
   * Génère des recommandations personnalisées pour l'utilisateur
   */
  static generateRecommendations(
    userProgress: any,
    recentAttempts: LevelAttempt[]
  ): {
    nextLevel: number | null;
    focusAreas: string[];
    studyTips: string[];
    estimatedStudyTime: number;
  } {
    const focusAreas: string[] = [];
    const studyTips: string[] = [];
    
    // Analyser les performances récentes
    const recentScores = recentAttempts.map(a => (a.totalScore / a.maxScore) * 100);
    const averageRecentScore = recentScores.length > 0 
      ? recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length 
      : 0;
    
    if (averageRecentScore < 70) {
      focusAreas.push("Révision des concepts fondamentaux");
      studyTips.push("Prenez plus de temps pour analyser les énoncés");
      studyTips.push("Utilisez les indices disponibles sans hésiter");
    }
    
    if (averageRecentScore >= 70 && averageRecentScore < 85) {
      focusAreas.push("Perfectionnement des calculs");
      studyTips.push("Vérifiez vos calculs une seconde fois");
    }
    
    if (averageRecentScore >= 85) {
      focusAreas.push("Défis avancés");
      studyTips.push("Essayez de résoudre sans indices");
      studyTips.push("Concentrez-vous sur la rapidité d'exécution");
    }
    
    // Temps d'étude estimé basé sur les performances
    const estimatedStudyTime = averageRecentScore < 70 ? 45 : 
                              averageRecentScore < 85 ? 30 : 20;
    
    const nextLevel = this.getNextRecommendedLevel(
      userProgress.currentLevel,
      recentAttempts[0]?.totalScore || 0,
      recentAttempts[0]?.maxScore || 100,
      userProgress.completedLevels
    );
    
    return {
      nextLevel,
      focusAreas,
      studyTips,
      estimatedStudyTime
    };
  }
}
