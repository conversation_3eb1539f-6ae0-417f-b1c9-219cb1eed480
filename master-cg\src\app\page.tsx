import Link from "next/link";
import { Play, Trophy, BookOpen, Users, Target, Zap } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Master CG
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Ma<PERSON><PERSON><PERSON><PERSON> le contrôle de gestion avec 300 niveaux progressifs
            </p>
            <p className="text-lg mb-12 text-blue-200 max-w-3xl mx-auto">
              Une plateforme d'apprentissage interactive qui transforme l'apprentissage du contrôle de gestion
              en une expérience engageante et progressive, avec des cas pratiques réels et un feedback intelligent.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/dashboard"
                className="inline-flex items-center px-8 py-4 bg-white text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                <Play className="mr-2 h-5 w-5" />
                Commencer maintenant
              </Link>
              <Link
                href="/levels"
                className="inline-flex items-center px-8 py-4 border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                <BookOpen className="mr-2 h-5 w-5" />
                Explorer les niveaux
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Pourquoi choisir Master CG ?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Une approche révolutionnaire pour apprendre le contrôle de gestion
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">300 Niveaux Progressifs</h3>
              <p className="text-gray-600">
                Du débutant à l'expert, progressez à votre rythme avec des niveaux soigneusement conçus
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Feedback IA Intelligent</h3>
              <p className="text-gray-600">
                Recevez des corrections personnalisées et des conseils d'amélioration grâce à l'IA
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Cas Pratiques Réels</h3>
              <p className="text-gray-600">
                Apprenez avec des scénarios d'entreprises réelles et des situations concrètes
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Système de Récompenses</h3>
              <p className="text-gray-600">
                Débloquez des badges et suivez votre progression avec un système motivant
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Suivi Personnalisé</h3>
              <p className="text-gray-600">
                Analysez vos forces et faiblesses avec des statistiques détaillées
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Play className="h-8 w-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Interface Intuitive</h3>
              <p className="text-gray-600">
                Une expérience utilisateur fluide et engageante pour un apprentissage optimal
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Prêt à devenir un expert en contrôle de gestion ?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Rejoignez des milliers d'étudiants et professionnels qui ont déjà transformé leur carrière
          </p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors text-lg"
          >
            <Play className="mr-2 h-6 w-6" />
            Commencer gratuitement
          </Link>
        </div>
      </section>
    </div>
  );
}
