// Utilitaires pour optimiser les performances de la plateforme

/**
 * Cache en mémoire pour les niveaux fréquemment accédés
 */
class LevelCache {
  private cache = new Map<number, any>();
  private maxSize = 50; // Limiter le cache à 50 niveaux
  private accessOrder: number[] = [];

  get(levelId: number) {
    const level = this.cache.get(levelId);
    if (level) {
      // Mettre à jour l'ordre d'accès (LRU)
      this.accessOrder = this.accessOrder.filter(id => id !== levelId);
      this.accessOrder.push(levelId);
    }
    return level;
  }

  set(levelId: number, level: any) {
    // Si le cache est plein, supprimer le moins récemment utilisé
    if (this.cache.size >= this.maxSize && !this.cache.has(levelId)) {
      const lruId = this.accessOrder.shift();
      if (lruId !== undefined) {
        this.cache.delete(lruId);
      }
    }

    this.cache.set(levelId, level);
    this.accessOrder = this.accessOrder.filter(id => id !== levelId);
    this.accessOrder.push(levelId);
  }

  clear() {
    this.cache.clear();
    this.accessOrder = [];
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.accessOrder.length > 0 ? this.cache.size / this.accessOrder.length : 0
    };
  }
}

export const levelCache = new LevelCache();

/**
 * Précharge les niveaux suivants pour améliorer l'expérience utilisateur
 */
export async function preloadNextLevels(currentLevel: number, count: number = 3) {
  const nextLevels = Array.from({ length: count }, (_, i) => currentLevel + i + 1)
    .filter(level => level <= 300);

  const preloadPromises = nextLevels.map(async (levelId) => {
    if (!levelCache.get(levelId)) {
      try {
        const response = await fetch(`/api/levels/${levelId}`);
        if (response.ok) {
          const data = await response.json();
          levelCache.set(levelId, data.data);
        }
      } catch (error) {
        console.warn(`Erreur lors du préchargement du niveau ${levelId}:`, error);
      }
    }
  });

  await Promise.allSettled(preloadPromises);
}

/**
 * Optimise les images et ressources
 */
export function optimizeImageLoading() {
  // Lazy loading pour les images
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
}

/**
 * Mesure les performances de l'application
 */
export class PerformanceMonitor {
  private metrics: Record<string, number[]> = {};

  startTiming(label: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.metrics[label]) {
        this.metrics[label] = [];
      }
      this.metrics[label].push(duration);
      
      // Garder seulement les 100 dernières mesures
      if (this.metrics[label].length > 100) {
        this.metrics[label] = this.metrics[label].slice(-100);
      }
    };
  }

  getMetrics(label: string) {
    const times = this.metrics[label] || [];
    if (times.length === 0) return null;

    const sorted = [...times].sort((a, b) => a - b);
    return {
      count: times.length,
      average: times.reduce((sum, time) => sum + time, 0) / times.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      min: sorted[0],
      max: sorted[sorted.length - 1]
    };
  }

  getAllMetrics() {
    const result: Record<string, any> = {};
    Object.keys(this.metrics).forEach(label => {
      result[label] = this.getMetrics(label);
    });
    return result;
  }

  clear() {
    this.metrics = {};
  }
}

export const performanceMonitor = new PerformanceMonitor();

/**
 * Optimise les requêtes API avec debouncing
 */
export function createDebouncedApiCall<T extends any[], R>(
  apiCall: (...args: T) => Promise<R>,
  delay: number = 300
): (...args: T) => Promise<R> {
  let timeoutId: NodeJS.Timeout;
  let latestResolve: (value: R) => void;
  let latestReject: (reason: any) => void;

  return (...args: T): Promise<R> => {
    return new Promise<R>((resolve, reject) => {
      latestResolve = resolve;
      latestReject = reject;

      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        try {
          const result = await apiCall(...args);
          latestResolve(result);
        } catch (error) {
          latestReject(error);
        }
      }, delay);
    });
  };
}

/**
 * Optimise le rendu des listes longues avec virtualisation
 */
export function calculateVisibleItems(
  containerHeight: number,
  itemHeight: number,
  scrollTop: number,
  totalItems: number,
  overscan: number = 5
) {
  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(totalItems - 1, startIndex + visibleCount + overscan * 2);

  return {
    startIndex,
    endIndex,
    visibleCount: endIndex - startIndex + 1,
    offsetY: startIndex * itemHeight
  };
}

/**
 * Optimise les animations avec requestAnimationFrame
 */
export function smoothScroll(element: HTMLElement, to: number, duration: number = 300) {
  const start = element.scrollTop;
  const change = to - start;
  const startTime = performance.now();

  function animateScroll(currentTime: number) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // Fonction d'easing (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3);
    
    element.scrollTop = start + change * easeOut;

    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  }

  requestAnimationFrame(animateScroll);
}

/**
 * Optimise le stockage local avec compression
 */
export class OptimizedStorage {
  private prefix: string;

  constructor(prefix: string = 'mastercg_') {
    this.prefix = prefix;
  }

  set(key: string, value: any, ttl?: number): void {
    const item = {
      value,
      timestamp: Date.now(),
      ttl: ttl ? Date.now() + ttl : null
    };

    try {
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Erreur lors de la sauvegarde en localStorage:', error);
      // Nettoyer le localStorage si plein
      this.cleanup();
      try {
        localStorage.setItem(this.prefix + key, JSON.stringify(item));
      } catch (retryError) {
        console.error('Impossible de sauvegarder en localStorage:', retryError);
      }
    }
  }

  get<T>(key: string): T | null {
    try {
      const itemStr = localStorage.getItem(this.prefix + key);
      if (!itemStr) return null;

      const item = JSON.parse(itemStr);
      
      // Vérifier l'expiration
      if (item.ttl && Date.now() > item.ttl) {
        localStorage.removeItem(this.prefix + key);
        return null;
      }

      return item.value;
    } catch (error) {
      console.warn('Erreur lors de la lecture du localStorage:', error);
      return null;
    }
  }

  remove(key: string): void {
    localStorage.removeItem(this.prefix + key);
  }

  cleanup(): void {
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '{}');
          if (item.ttl && Date.now() > item.ttl) {
            keysToRemove.push(key);
          }
        } catch (error) {
          // Supprimer les éléments corrompus
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  getStats() {
    let totalSize = 0;
    let itemCount = 0;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        const value = localStorage.getItem(key);
        if (value) {
          totalSize += value.length;
          itemCount++;
        }
      }
    }

    return {
      itemCount,
      totalSize,
      totalSizeKB: Math.round(totalSize / 1024 * 100) / 100
    };
  }
}

export const optimizedStorage = new OptimizedStorage();
