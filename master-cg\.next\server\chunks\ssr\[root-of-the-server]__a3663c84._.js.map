{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/CG/master-cg/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { \n  Play, \n  Trophy, \n  BookOpen, \n  TrendingUp, \n  Clock, \n  Target,\n  Star,\n  ChevronRight,\n  Calendar,\n  Award\n} from \"lucide-react\";\n\n// Données mockées pour la démonstration\nconst mockUserProgress = {\n  currentLevel: 15,\n  totalScore: 2450,\n  completedLevels: 14,\n  totalLevels: 300,\n  streakDays: 7,\n  averageScore: 85,\n  timeSpent: 420, // en minutes\n  lastPlayedAt: new Date(),\n};\n\nconst mockRecentLevels = [\n  { id: 14, title: \"Analyse des écarts budgétaires\", score: 92, maxScore: 100, completedAt: new Date() },\n  { id: 13, title: \"Budget de trésorerie\", score: 88, maxScore: 100, completedAt: new Date(Date.now() - 86400000) },\n  { id: 12, title: \"Coûts préétablis\", score: 95, maxScore: 100, completedAt: new Date(Date.now() - 172800000) },\n];\n\nconst mockAchievements = [\n  { id: 1, name: \"Premier pas\", icon: \"🎯\", unlockedAt: new Date() },\n  { id: 2, name: \"Série de 5\", icon: \"🔥\", unlockedAt: new Date() },\n  { id: 3, name: \"Score parfait\", icon: \"⭐\", unlockedAt: new Date() },\n];\n\nconst mockUpcomingLevels = [\n  { id: 15, title: \"Seuil de rentabilité multi-produits\", difficulty: \"Intermédiaire\", estimatedTime: 25 },\n  { id: 16, title: \"Analyse de la contribution marginale\", difficulty: \"Intermédiaire\", estimatedTime: 30 },\n  { id: 17, title: \"Choix d'investissement - VAN\", difficulty: \"Avancé\", estimatedTime: 35 },\n];\n\nexport default function Dashboard() {\n  const [selectedTab, setSelectedTab] = useState(\"overview\");\n\n  const progressPercentage = (mockUserProgress.completedLevels / mockUserProgress.totalLevels) * 100;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n                Master CG\n              </Link>\n              <nav className=\"hidden md:flex space-x-6\">\n                <Link href=\"/dashboard\" className=\"text-blue-600 font-medium\">Dashboard</Link>\n                <Link href=\"/levels\" className=\"text-gray-600 hover:text-gray-900\">Niveaux</Link>\n                <Link href=\"/progress\" className=\"text-gray-600 hover:text-gray-900\">Progression</Link>\n                <Link href=\"/achievements\" className=\"text-gray-600 hover:text-gray-900\">Succès</Link>\n              </nav>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-sm text-gray-600\">\n                Niveau {mockUserProgress.currentLevel}\n              </div>\n              <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold\">\n                U\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Bon retour ! 👋\n          </h1>\n          <p className=\"text-gray-600\">\n            Continuez votre parcours d'apprentissage en contrôle de gestion\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Niveau actuel</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockUserProgress.currentLevel}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                <Target className=\"h-6 w-6 text-blue-600\" />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Score total</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockUserProgress.totalScore.toLocaleString()}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                <Trophy className=\"h-6 w-6 text-green-600\" />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Série actuelle</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockUserProgress.streakDays} jours</p>\n              </div>\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n                <Calendar className=\"h-6 w-6 text-orange-600\" />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Score moyen</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{mockUserProgress.averageScore}%</p>\n              </div>\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n                <TrendingUp className=\"h-6 w-6 text-purple-600\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Progress Section */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-xl font-semibold text-gray-900\">Votre progression</h2>\n                <span className=\"text-sm text-gray-600\">\n                  {mockUserProgress.completedLevels}/{mockUserProgress.totalLevels} niveaux\n                </span>\n              </div>\n              <div className=\"mb-4\">\n                <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\n                  <span>Progression globale</span>\n                  <span>{progressPercentage.toFixed(1)}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                  <div \n                    className=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                    style={{ width: `${progressPercentage}%` }}\n                  ></div>\n                </div>\n              </div>\n              <Link \n                href={`/level/${mockUserProgress.currentLevel}`}\n                className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                <Play className=\"mr-2 h-5 w-5\" />\n                Continuer le niveau {mockUserProgress.currentLevel}\n              </Link>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Activité récente</h2>\n              <div className=\"space-y-4\">\n                {mockRecentLevels.map((level) => (\n                  <div key={level.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                        <Star className=\"h-5 w-5 text-green-600\" />\n                      </div>\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">Niveau {level.id}</h3>\n                        <p className=\"text-sm text-gray-600\">{level.title}</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-semibold text-gray-900\">\n                        {level.score}/{level.maxScore}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">\n                        {Math.round((level.score / level.maxScore) * 100)}%\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-8\">\n            {/* Next Levels */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Prochains niveaux</h2>\n              <div className=\"space-y-3\">\n                {mockUpcomingLevels.map((level) => (\n                  <Link\n                    key={level.id}\n                    href={`/level/${level.id}`}\n                    className=\"block p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors\"\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">Niveau {level.id}</h3>\n                        <p className=\"text-sm text-gray-600\">{level.title}</p>\n                        <div className=\"flex items-center space-x-2 mt-1\">\n                          <span className=\"text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded\">\n                            {level.difficulty}\n                          </span>\n                          <span className=\"text-xs text-gray-500 flex items-center\">\n                            <Clock className=\"h-3 w-3 mr-1\" />\n                            {level.estimatedTime}min\n                          </span>\n                        </div>\n                      </div>\n                      <ChevronRight className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* Recent Achievements */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Succès récents</h2>\n              <div className=\"space-y-3\">\n                {mockAchievements.map((achievement) => (\n                  <div key={achievement.id} className=\"flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg\">\n                    <div className=\"text-2xl\">{achievement.icon}</div>\n                    <div>\n                      <h3 className=\"font-medium text-gray-900\">{achievement.name}</h3>\n                      <p className=\"text-sm text-gray-600\">Débloqué récemment</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <Link \n                href=\"/achievements\"\n                className=\"block text-center mt-4 text-blue-600 hover:text-blue-700 font-medium\"\n              >\n                Voir tous les succès\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBA,wCAAwC;AACxC,MAAM,mBAAmB;IACvB,cAAc;IACd,YAAY;IACZ,iBAAiB;IACjB,aAAa;IACb,YAAY;IACZ,cAAc;IACd,WAAW;IACX,cAAc,IAAI;AACpB;AAEA,MAAM,mBAAmB;IACvB;QAAE,IAAI;QAAI,OAAO;QAAkC,OAAO;QAAI,UAAU;QAAK,aAAa,IAAI;IAAO;IACrG;QAAE,IAAI;QAAI,OAAO;QAAwB,OAAO;QAAI,UAAU;QAAK,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;IAAU;IAChH;QAAE,IAAI;QAAI,OAAO;QAAoB,OAAO;QAAI,UAAU;QAAK,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK;IAAW;CAC9G;AAED,MAAM,mBAAmB;IACvB;QAAE,IAAI;QAAG,MAAM;QAAe,MAAM;QAAM,YAAY,IAAI;IAAO;IACjE;QAAE,IAAI;QAAG,MAAM;QAAc,MAAM;QAAM,YAAY,IAAI;IAAO;IAChE;QAAE,IAAI;QAAG,MAAM;QAAiB,MAAM;QAAK,YAAY,IAAI;IAAO;CACnE;AAED,MAAM,qBAAqB;IACzB;QAAE,IAAI;QAAI,OAAO;QAAuC,YAAY;QAAiB,eAAe;IAAG;IACvG;QAAE,IAAI;QAAI,OAAO;QAAwC,YAAY;QAAiB,eAAe;IAAG;IACxG;QAAE,IAAI;QAAI,OAAO;QAAgC,YAAY;QAAU,eAAe;IAAG;CAC1F;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,AAAC,iBAAiB,eAAe,GAAG,iBAAiB,WAAW,GAAI;IAE/F,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAA4B;;;;;;0DAC9D,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAoC;;;;;;0DACnE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAoC;;;;;;0DACrE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAoC;;;;;;;;;;;;;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAwB;4CAC7B,iBAAiB,YAAY;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;kDAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpH,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAoC,iBAAiB,YAAY;;;;;;;;;;;;sDAEhF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAoC,iBAAiB,UAAU,CAAC,cAAc;;;;;;;;;;;;sDAE7F,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAoC,iBAAiB,UAAU;wDAAC;;;;;;;;;;;;;sDAE/E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAoC,iBAAiB,YAAY;wDAAC;;;;;;;;;;;;;sDAEjF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAK,WAAU;;4DACb,iBAAiB,eAAe;4DAAC;4DAAE,iBAAiB,WAAW;4DAAC;;;;;;;;;;;;;0DAGrE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,mBAAmB,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4DAAC;;;;;;;;;;;;;;;;;0DAI/C,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,iBAAiB,YAAY,EAAE;gDAC/C,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;oDACZ,iBAAiB,YAAY;;;;;;;;;;;;;kDAKtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,sBACrB,8OAAC;wDAAmB,WAAU;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;;oFAA4B;oFAAQ,MAAM,EAAE;;;;;;;0FAC1D,8OAAC;gFAAE,WAAU;0FAAyB,MAAM,KAAK;;;;;;;;;;;;;;;;;;0EAGrD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,MAAM,KAAK;4EAAC;4EAAE,MAAM,QAAQ;;;;;;;kFAE/B,8OAAC;wEAAI,WAAU;;4EACZ,KAAK,KAAK,CAAC,AAAC,MAAM,KAAK,GAAG,MAAM,QAAQ,GAAI;4EAAK;;;;;;;;;;;;;;uDAf9C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAyB1B,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,GAAG,CAAC,CAAC,sBACvB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wDAC1B,WAAU;kEAEV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;;gFAA4B;gFAAQ,MAAM,EAAE;;;;;;;sFAC1D,8OAAC;4EAAE,WAAU;sFAAyB,MAAM,KAAK;;;;;;sFACjD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FACb,MAAM,UAAU;;;;;;8FAEnB,8OAAC;oFAAK,WAAU;;sGACd,8OAAC,oMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;wFAChB,MAAM,aAAa;wFAAC;;;;;;;;;;;;;;;;;;;8EAI3B,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;uDAlBrB,MAAM,EAAE;;;;;;;;;;;;;;;;kDA0BrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,4BACrB,8OAAC;wDAAyB,WAAU;;0EAClC,8OAAC;gEAAI,WAAU;0EAAY,YAAY,IAAI;;;;;;0EAC3C,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,YAAY,IAAI;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;uDAJ/B,YAAY,EAAE;;;;;;;;;;0DAS5B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}