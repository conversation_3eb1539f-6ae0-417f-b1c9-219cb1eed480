{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/CG/master-cg/src/types/index.ts"], "sourcesContent": ["// Types pour la plateforme Master CG\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  avatar?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  progress: UserProgress;\n  achievements: Achievement[];\n}\n\nexport interface UserProgress {\n  id: string;\n  userId: string;\n  currentLevel: number;\n  totalScore: number;\n  completedLevels: number[];\n  unlockedLevels: number[];\n  streakDays: number;\n  lastPlayedAt: Date;\n  totalTimeSpent: number; // en minutes\n}\n\nexport interface Level {\n  id: number;\n  title: string;\n  description: string;\n  category: LevelCategory;\n  difficulty: Difficulty;\n  estimatedTime: number; // en minutes\n  prerequisites: number[]; // IDs des niveaux prérequis\n  scenario: string;\n  context: LevelContext;\n  questions: Question[];\n  maxScore: number;\n  hints: string[];\n  resources: Resource[];\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface LevelContext {\n  company: string;\n  sector: string;\n  situation: string;\n  documents: Document[];\n  data: Record<string, any>;\n}\n\nexport interface Document {\n  id: string;\n  name: string;\n  type: DocumentType;\n  content: string;\n  url?: string;\n}\n\nexport interface Question {\n  id: string;\n  type: QuestionType;\n  question: string;\n  options?: string[]; // Pour les QCM\n  correctAnswer: string | string[];\n  explanation: string;\n  points: number;\n  tolerance?: number; // Pour les réponses numériques\n}\n\nexport interface UserAnswer {\n  questionId: string;\n  answer: string | string[];\n  isCorrect: boolean;\n  pointsEarned: number;\n  timeSpent: number; // en secondes\n  hintsUsed: number;\n}\n\nexport interface LevelAttempt {\n  id: string;\n  userId: string;\n  levelId: number;\n  answers: UserAnswer[];\n  totalScore: number;\n  maxScore: number;\n  percentage: number;\n  timeSpent: number; // en secondes\n  hintsUsed: number;\n  completed: boolean;\n  startedAt: Date;\n  completedAt?: Date;\n  feedback: AIFeedback;\n}\n\nexport interface AIFeedback {\n  id: string;\n  attemptId: string;\n  overallFeedback: string;\n  strengths: string[];\n  improvements: string[];\n  recommendations: string[];\n  nextLevelSuggestion?: number;\n  generatedAt: Date;\n}\n\nexport interface Achievement {\n  id: string;\n  name: string;\n  description: string;\n  icon: string;\n  category: AchievementCategory;\n  condition: AchievementCondition;\n  reward: number; // points bonus\n  unlockedAt?: Date;\n}\n\nexport interface Resource {\n  id: string;\n  title: string;\n  type: ResourceType;\n  content: string;\n  url?: string;\n  downloadable: boolean;\n}\n\n// Enums\nexport enum LevelCategory {\n  FONDAMENTAUX = 'FONDAMENTAUX',\n  BUDGET_CLOTURE = 'BUDGET_CLOTURE',\n  ANALYSE_DECISION = 'ANALYSE_DECISION',\n  CONTROLE_INDUSTRIEL = 'CONTROLE_INDUSTRIEL',\n  PILOTAGE_STRATEGIQUE = 'PILOTAGE_STRATEGIQUE'\n}\n\nexport enum Difficulty {\n  DEBUTANT = 'DEBUTANT',\n  INTERMEDIAIRE = 'INTERMEDIAIRE',\n  AVANCE = 'AVANCE',\n  EXPERT = 'EXPERT'\n}\n\nexport enum QuestionType {\n  QCM = 'QCM',\n  QCM_MULTIPLE = 'QCM_MULTIPLE',\n  NUMERIQUE = 'NUMERIQUE',\n  TEXTE_LIBRE = 'TEXTE_LIBRE',\n  CALCUL = 'CALCUL',\n  TABLEAU = 'TABLEAU'\n}\n\nexport enum DocumentType {\n  BALANCE = 'BALANCE',\n  COMPTE_RESULTAT = 'COMPTE_RESULTAT',\n  BUDGET = 'BUDGET',\n  FACTURE = 'FACTURE',\n  CONTRAT = 'CONTRAT',\n  RAPPORT = 'RAPPORT',\n  TABLEAU_BORD = 'TABLEAU_BORD'\n}\n\nexport enum AchievementCategory {\n  PROGRESSION = 'PROGRESSION',\n  PERFORMANCE = 'PERFORMANCE',\n  REGULARITE = 'REGULARITE',\n  EXPERTISE = 'EXPERTISE',\n  SOCIAL = 'SOCIAL'\n}\n\nexport enum ResourceType {\n  COURS = 'COURS',\n  EXERCICE = 'EXERCICE',\n  TEMPLATE = 'TEMPLATE',\n  VIDEO = 'VIDEO',\n  ARTICLE = 'ARTICLE'\n}\n\nexport interface AchievementCondition {\n  type: 'LEVELS_COMPLETED' | 'SCORE_THRESHOLD' | 'STREAK_DAYS' | 'CATEGORY_MASTERY' | 'PERFECT_SCORE';\n  value: number;\n  category?: LevelCategory;\n}\n\n// Types pour les statistiques et analytics\nexport interface UserStats {\n  totalLevelsCompleted: number;\n  averageScore: number;\n  totalTimeSpent: number;\n  currentStreak: number;\n  longestStreak: number;\n  categoryProgress: Record<LevelCategory, CategoryProgress>;\n  recentActivity: ActivityEntry[];\n}\n\nexport interface CategoryProgress {\n  category: LevelCategory;\n  completedLevels: number;\n  totalLevels: number;\n  averageScore: number;\n  timeSpent: number;\n}\n\nexport interface ActivityEntry {\n  id: string;\n  type: 'LEVEL_COMPLETED' | 'ACHIEVEMENT_UNLOCKED' | 'STREAK_MILESTONE';\n  description: string;\n  timestamp: Date;\n  points?: number;\n}\n\n// Types pour l'API\nexport interface APIResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n\n// Types pour les composants UI\nexport interface GameState {\n  currentLevel: Level | null;\n  currentAttempt: LevelAttempt | null;\n  isLoading: boolean;\n  error: string | null;\n  timeRemaining?: number;\n  hintsRemaining: number;\n}\n\nexport interface NavigationItem {\n  id: string;\n  label: string;\n  href: string;\n  icon: string;\n  badge?: number;\n  active?: boolean;\n}\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;;;AA+H9B,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAOL,IAAA,AAAK,sCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,sCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,6CAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,sCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/CG/master-cg/src/app/level/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useParams, useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\nimport { \n  ArrowLeft, \n  Clock, \n  HelpCircle, \n  CheckCircle, \n  XCircle,\n  Lightbulb,\n  FileText,\n  Target\n} from \"lucide-react\";\nimport { QuestionType } from \"@/types\";\nimport { SAMPLE_LEVELS } from \"@/data/sample-levels\";\n\n// Données mockées pour la démonstration\nconst mockLevel = {\n  id: 1,\n  title: \"Calcul du coût unitaire - Boulangerie Dupont\",\n  description: \"Apprenez à calculer le coût unitaire d'un produit avec un cas pratique simple\",\n  scenario: `\nLa boulangerie Dupont souhaite calculer le coût unitaire de ses baguettes traditionnelles.\nVous êtes consultant en contrôle de gestion et devez l'aider dans cette démarche.\n  `,\n  context: {\n    company: \"Boulangerie Dupont\",\n    sector: \"Boulangerie-Pâtisserie\",\n    situation: \"Calcul de coût unitaire pour optimiser la rentabilité\",\n    documents: [\n      {\n        id: \"doc1\",\n        name: \"Fiche de coûts - Baguettes\",\n        type: \"RAPPORT\",\n        content: `\nCOÛTS DE PRODUCTION - BAGUETTES TRADITIONNELLES\nPériode: Janvier 2024\nProduction: 1 000 baguettes\n\nMATIÈRES PREMIÈRES:\n- Farine: 200 kg à 0,80€/kg = 160€\n- Levure: 2 kg à 3,50€/kg = 7€\n- Sel: 3 kg à 0,30€/kg = 0,90€\n- Eau: négligeable\n\nMAIN D'ŒUVRE DIRECTE:\n- Boulanger: 20h à 18€/h = 360€\n\nCHARGES INDIRECTES:\n- Électricité four: 80€\n- Amortissement matériel: 50€\n- Autres charges: 42,10€\n\nTOTAL DES COÛTS: ?\nCOÛT UNITAIRE: ?\n        `\n      }\n    ]\n  },\n  questions: [\n    {\n      id: \"q1\",\n      type: QuestionType.NUMERIQUE,\n      question: \"Quel est le montant total des matières premières utilisées ?\",\n      correctAnswer: \"167.90\",\n      explanation: \"Farine (160€) + Levure (7€) + Sel (0,90€) = 167,90€\",\n      points: 25,\n      tolerance: 0.1\n    },\n    {\n      id: \"q2\",\n      type: QuestionType.NUMERIQUE,\n      question: \"Quel est le coût total de production des 1000 baguettes ?\",\n      correctAnswer: \"700\",\n      explanation: \"Matières premières (167,90€) + Main d'œuvre (360€) + Charges indirectes (172,10€) = 700€\",\n      points: 35,\n      tolerance: 0.1\n    },\n    {\n      id: \"q3\",\n      type: QuestionType.NUMERIQUE,\n      question: \"Quel est le coût unitaire d'une baguette (en euros) ?\",\n      correctAnswer: \"0.70\",\n      explanation: \"Coût total (700€) ÷ Quantité produite (1000) = 0,70€ par baguette\",\n      points: 40,\n      tolerance: 0.01\n    }\n  ],\n  maxScore: 100,\n  hints: [\n    \"Commencez par additionner tous les coûts de matières premières\",\n    \"N'oubliez pas d'inclure tous les types de coûts : matières, main d'œuvre et charges indirectes\",\n    \"Le coût unitaire = Coût total ÷ Nombre d'unités produites\"\n  ],\n  estimatedTime: 15\n};\n\nexport default function LevelPage() {\n  const params = useParams();\n  const router = useRouter();\n  const levelId = parseInt(params.id as string);\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState<Record<string, string>>({});\n  const [showHint, setShowHint] = useState(false);\n  const [hintsUsed, setHintsUsed] = useState(0);\n  const [timeSpent, setTimeSpent] = useState(0);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const [results, setResults] = useState<any>(null);\n  const [startTime] = useState(Date.now());\n\n  // Timer\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [startTime]);\n\n  const currentQuestion = mockLevel.questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === mockLevel.questions.length - 1;\n\n  const handleAnswerChange = (value: string) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestion.id]: value\n    }));\n  };\n\n  const handleNext = () => {\n    if (isLastQuestion) {\n      handleSubmit();\n    } else {\n      setCurrentQuestionIndex(prev => prev + 1);\n      setShowHint(false);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n      setShowHint(false);\n    }\n  };\n\n  const handleHint = () => {\n    setShowHint(true);\n    setHintsUsed(prev => prev + 1);\n  };\n\n  const validateAnswer = (question: any, userAnswer: string) => {\n    if (question.type === QuestionType.NUMERIQUE) {\n      const userNum = parseFloat(userAnswer.replace(/[^\\d.-]/g, ''));\n      const correctNum = parseFloat(question.correctAnswer.replace(/[^\\d.-]/g, ''));\n      \n      if (isNaN(userNum) || isNaN(correctNum)) return false;\n      \n      const tolerance = question.tolerance || 0;\n      const diff = Math.abs(userNum - correctNum);\n      const toleranceValue = Math.abs(correctNum * tolerance / 100);\n      \n      return diff <= toleranceValue;\n    }\n    \n    return userAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();\n  };\n\n  const handleSubmit = () => {\n    let totalScore = 0;\n    const questionResults = mockLevel.questions.map(question => {\n      const userAnswer = answers[question.id] || \"\";\n      const isCorrect = validateAnswer(question, userAnswer);\n      const pointsEarned = isCorrect ? question.points : 0;\n      totalScore += pointsEarned;\n      \n      return {\n        questionId: question.id,\n        question: question.question,\n        userAnswer,\n        correctAnswer: question.correctAnswer,\n        isCorrect,\n        pointsEarned,\n        explanation: question.explanation\n      };\n    });\n\n    const finalResults = {\n      totalScore,\n      maxScore: mockLevel.maxScore,\n      percentage: Math.round((totalScore / mockLevel.maxScore) * 100),\n      timeSpent,\n      hintsUsed,\n      questions: questionResults\n    };\n\n    setResults(finalResults);\n    setIsCompleted(true);\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  if (isCompleted && results) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/dashboard\" className=\"flex items-center text-blue-600 hover:text-blue-700\">\n                <ArrowLeft className=\"mr-2 h-5 w-5\" />\n                Retour au dashboard\n              </Link>\n              <div className=\"text-sm text-gray-600\">\n                Niveau {levelId} terminé\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Results */}\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"bg-white rounded-lg p-8 shadow-sm\">\n            <div className=\"text-center mb-8\">\n              <div className={`w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${\n                results.percentage >= 80 ? 'bg-green-100' : \n                results.percentage >= 60 ? 'bg-yellow-100' : 'bg-red-100'\n              }`}>\n                {results.percentage >= 80 ? (\n                  <CheckCircle className=\"h-10 w-10 text-green-600\" />\n                ) : results.percentage >= 60 ? (\n                  <Target className=\"h-10 w-10 text-yellow-600\" />\n                ) : (\n                  <XCircle className=\"h-10 w-10 text-red-600\" />\n                )}\n              </div>\n              \n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Niveau terminé !\n              </h1>\n              \n              <div className=\"text-6xl font-bold mb-4\">\n                <span className={\n                  results.percentage >= 80 ? 'text-green-600' : \n                  results.percentage >= 60 ? 'text-yellow-600' : 'text-red-600'\n                }>\n                  {results.percentage}%\n                </span>\n              </div>\n              \n              <p className=\"text-xl text-gray-600\">\n                {results.totalScore}/{results.maxScore} points\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\">\n              <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-gray-900\">{formatTime(results.timeSpent)}</div>\n                <div className=\"text-sm text-gray-600\">Temps passé</div>\n              </div>\n              <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-gray-900\">{results.hintsUsed}</div>\n                <div className=\"text-sm text-gray-600\">Indices utilisés</div>\n              </div>\n              <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-gray-900\">\n                  {results.questions.filter((q: any) => q.isCorrect).length}\n                </div>\n                <div className=\"text-sm text-gray-600\">Bonnes réponses</div>\n              </div>\n              <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                <div className=\"text-2xl font-bold text-gray-900\">{mockLevel.questions.length}</div>\n                <div className=\"text-sm text-gray-600\">Questions total</div>\n              </div>\n            </div>\n\n            {/* Question Details */}\n            <div className=\"space-y-4 mb-8\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">Détail des réponses</h2>\n              {results.questions.map((result: any, index: number) => (\n                <div key={result.questionId} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <h3 className=\"font-medium text-gray-900\">Question {index + 1}</h3>\n                    <div className={`flex items-center space-x-1 ${\n                      result.isCorrect ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {result.isCorrect ? (\n                        <CheckCircle className=\"h-5 w-5\" />\n                      ) : (\n                        <XCircle className=\"h-5 w-5\" />\n                      )}\n                      <span className=\"font-medium\">\n                        {result.pointsEarned}/{mockLevel.questions[index].points} pts\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <p className=\"text-gray-700 mb-3\">{result.question}</p>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-4 mb-3\">\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-600\">Votre réponse:</span>\n                      <p className={`mt-1 ${result.isCorrect ? 'text-green-700' : 'text-red-700'}`}>\n                        {result.userAnswer || \"Pas de réponse\"}\n                      </p>\n                    </div>\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-600\">Réponse correcte:</span>\n                      <p className=\"mt-1 text-green-700\">{result.correctAnswer}</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"bg-blue-50 p-3 rounded\">\n                    <span className=\"text-sm font-medium text-blue-800\">Explication:</span>\n                    <p className=\"mt-1 text-blue-700\">{result.explanation}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link\n                href=\"/dashboard\"\n                className=\"px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center\"\n              >\n                Retour au dashboard\n              </Link>\n              <Link\n                href={`/level/${levelId + 1}`}\n                className=\"px-6 py-3 border border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center\"\n              >\n                Niveau suivant\n              </Link>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n              >\n                Recommencer\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/levels\" className=\"flex items-center text-blue-600 hover:text-blue-700\">\n              <ArrowLeft className=\"mr-2 h-5 w-5\" />\n              Retour aux niveaux\n            </Link>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                <Clock className=\"h-4 w-4\" />\n                <span>{formatTime(timeSpent)}</span>\n              </div>\n              <div className=\"text-sm text-gray-600\">\n                Question {currentQuestionIndex + 1}/{mockLevel.questions.length}\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {/* Level Info */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                {mockLevel.title}\n              </h1>\n              <p className=\"text-gray-600 mb-4\">{mockLevel.description}</p>\n              \n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <h2 className=\"font-semibold text-blue-900 mb-2\">Scénario</h2>\n                <p className=\"text-blue-800\">{mockLevel.scenario}</p>\n              </div>\n            </div>\n\n            {/* Question */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-xl font-semibold text-gray-900\">\n                  Question {currentQuestionIndex + 1}\n                </h2>\n                <span className=\"text-sm text-gray-600\">\n                  {currentQuestion.points} points\n                </span>\n              </div>\n              \n              <p className=\"text-gray-700 mb-6\">{currentQuestion.question}</p>\n              \n              {currentQuestion.type === QuestionType.NUMERIQUE && (\n                <div className=\"mb-6\">\n                  <input\n                    type=\"text\"\n                    value={answers[currentQuestion.id] || \"\"}\n                    onChange={(e) => handleAnswerChange(e.target.value)}\n                    placeholder=\"Entrez votre réponse numérique\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg\"\n                  />\n                  <p className=\"mt-2 text-sm text-gray-600\">\n                    Entrez uniquement la valeur numérique (ex: 167.90)\n                  </p>\n                </div>\n              )}\n\n              {showHint && (\n                <div className=\"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                  <div className=\"flex items-start space-x-2\">\n                    <Lightbulb className=\"h-5 w-5 text-yellow-600 mt-0.5\" />\n                    <div>\n                      <h3 className=\"font-medium text-yellow-800\">Indice</h3>\n                      <p className=\"text-yellow-700 mt-1\">\n                        {mockLevel.hints[Math.min(hintsUsed - 1, mockLevel.hints.length - 1)]}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Navigation */}\n              <div className=\"flex items-center justify-between\">\n                <button\n                  onClick={handlePrevious}\n                  disabled={currentQuestionIndex === 0}\n                  className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  Précédent\n                </button>\n\n                <button\n                  onClick={handleHint}\n                  className=\"flex items-center px-4 py-2 border border-yellow-300 text-yellow-700 rounded-lg font-medium hover:bg-yellow-50 transition-colors\"\n                >\n                  <HelpCircle className=\"mr-2 h-4 w-4\" />\n                  Indice ({hintsUsed})\n                </button>\n\n                <button\n                  onClick={handleNext}\n                  disabled={!answers[currentQuestion.id]}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isLastQuestion ? \"Terminer\" : \"Suivant\"}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Progress */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h3 className=\"font-semibold text-gray-900 mb-4\">Progression</h3>\n              <div className=\"space-y-2\">\n                {mockLevel.questions.map((_, index) => (\n                  <div\n                    key={index}\n                    className={`flex items-center justify-between p-2 rounded ${\n                      index === currentQuestionIndex\n                        ? 'bg-blue-100 text-blue-800'\n                        : answers[mockLevel.questions[index].id]\n                        ? 'bg-green-100 text-green-800'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}\n                  >\n                    <span>Question {index + 1}</span>\n                    {answers[mockLevel.questions[index].id] && (\n                      <CheckCircle className=\"h-4 w-4\" />\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Documents */}\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <h3 className=\"font-semibold text-gray-900 mb-4\">Documents</h3>\n              {mockLevel.context.documents.map((doc) => (\n                <div key={doc.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <FileText className=\"h-4 w-4 text-gray-600\" />\n                    <span className=\"font-medium text-gray-900\">{doc.name}</span>\n                  </div>\n                  <pre className=\"text-xs text-gray-700 whitespace-pre-wrap bg-gray-50 p-3 rounded overflow-x-auto\">\n                    {doc.content}\n                  </pre>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAfA;;;;;;AAkBA,wCAAwC;AACxC,MAAM,YAAY;IAChB,IAAI;IACJ,OAAO;IACP,aAAa;IACb,UAAU,CAAC;;;EAGX,CAAC;IACD,SAAS;QACP,SAAS;QACT,QAAQ;QACR,WAAW;QACX,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;QAqBV,CAAC;YACH;SACD;IACH;IACA,WAAW;QACT;YACE,IAAI;YACJ,MAAM,wHAAA,CAAA,eAAY,CAAC,SAAS;YAC5B,UAAU;YACV,eAAe;YACf,aAAa;YACb,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM,wHAAA,CAAA,eAAY,CAAC,SAAS;YAC5B,UAAU;YACV,eAAe;YACf,aAAa;YACb,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM,wHAAA,CAAA,eAAY,CAAC,SAAS;YAC5B,UAAU;YACV,eAAe;YACf,aAAa;YACb,QAAQ;YACR,WAAW;QACb;KACD;IACD,UAAU;IACV,OAAO;QACL;QACA;QACA;KACD;IACD,eAAe;AACjB;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU,SAAS,OAAO,EAAE;IAElC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;IAErC,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,QAAQ;6CAAY;oBACxB,aAAa,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;gBACrD;4CAAG;YAEH;uCAAO,IAAM,cAAc;;QAC7B;8BAAG;QAAC;KAAU;IAEd,MAAM,kBAAkB,UAAU,SAAS,CAAC,qBAAqB;IACjE,MAAM,iBAAiB,yBAAyB,UAAU,SAAS,CAAC,MAAM,GAAG;IAE7E,MAAM,qBAAqB,CAAC;QAC1B,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,gBAAgB,EAAE,CAAC,EAAE;YACxB,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB;YAClB;QACF,OAAO;YACL,wBAAwB,CAAA,OAAQ,OAAO;YACvC,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,uBAAuB,GAAG;YAC5B,wBAAwB,CAAA,OAAQ,OAAO;YACvC,YAAY;QACd;IACF;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,aAAa,CAAA,OAAQ,OAAO;IAC9B;IAEA,MAAM,iBAAiB,CAAC,UAAe;QACrC,IAAI,SAAS,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,SAAS,EAAE;YAC5C,MAAM,UAAU,WAAW,WAAW,OAAO,CAAC,YAAY;YAC1D,MAAM,aAAa,WAAW,SAAS,aAAa,CAAC,OAAO,CAAC,YAAY;YAEzE,IAAI,MAAM,YAAY,MAAM,aAAa,OAAO;YAEhD,MAAM,YAAY,SAAS,SAAS,IAAI;YACxC,MAAM,OAAO,KAAK,GAAG,CAAC,UAAU;YAChC,MAAM,iBAAiB,KAAK,GAAG,CAAC,aAAa,YAAY;YAEzD,OAAO,QAAQ;QACjB;QAEA,OAAO,WAAW,WAAW,GAAG,IAAI,OAAO,SAAS,aAAa,CAAC,WAAW,GAAG,IAAI;IACtF;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa;QACjB,MAAM,kBAAkB,UAAU,SAAS,CAAC,GAAG,CAAC,CAAA;YAC9C,MAAM,aAAa,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI;YAC3C,MAAM,YAAY,eAAe,UAAU;YAC3C,MAAM,eAAe,YAAY,SAAS,MAAM,GAAG;YACnD,cAAc;YAEd,OAAO;gBACL,YAAY,SAAS,EAAE;gBACvB,UAAU,SAAS,QAAQ;gBAC3B;gBACA,eAAe,SAAS,aAAa;gBACrC;gBACA;gBACA,aAAa,SAAS,WAAW;YACnC;QACF;QAEA,MAAM,eAAe;YACnB;YACA,UAAU,UAAU,QAAQ;YAC5B,YAAY,KAAK,KAAK,CAAC,AAAC,aAAa,UAAU,QAAQ,GAAI;YAC3D;YACA;YACA,WAAW;QACb;QAEA,WAAW;QACX,eAAe;IACjB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,6LAAC;oCAAI,WAAU;;wCAAwB;wCAC7B;wCAAQ;;;;;;;;;;;;;;;;;;;;;;;8BAOxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,qEAAqE,EACpF,QAAQ,UAAU,IAAI,KAAK,iBAC3B,QAAQ,UAAU,IAAI,KAAK,kBAAkB,cAC7C;kDACC,QAAQ,UAAU,IAAI,mBACrB,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;mDACrB,QAAQ,UAAU,IAAI,mBACxB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;iEAElB,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAIvB,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAItD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WACJ,QAAQ,UAAU,IAAI,KAAK,mBAC3B,QAAQ,UAAU,IAAI,KAAK,oBAAoB;;gDAE9C,QAAQ,UAAU;gDAAC;;;;;;;;;;;;kDAIxB,6LAAC;wCAAE,WAAU;;4CACV,QAAQ,UAAU;4CAAC;4CAAE,QAAQ,QAAQ;4CAAC;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,WAAW,QAAQ,SAAS;;;;;;0DAC/E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,QAAQ,SAAS;;;;;;0DACpE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAC,IAAW,EAAE,SAAS,EAAE,MAAM;;;;;;0DAE3D,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,UAAU,SAAS,CAAC,MAAM;;;;;;0DAC7E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;oCACnD,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,QAAa,sBACnC,6LAAC;4CAA4B,WAAU;;8DACrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;;gEAA4B;gEAAU,QAAQ;;;;;;;sEAC5D,6LAAC;4DAAI,WAAW,CAAC,4BAA4B,EAC3C,OAAO,SAAS,GAAG,mBAAmB,gBACtC;;gEACC,OAAO,SAAS,iBACf,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,+MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EAErB,6LAAC;oEAAK,WAAU;;wEACb,OAAO,YAAY;wEAAC;wEAAE,UAAU,SAAS,CAAC,MAAM,CAAC,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;8DAK/D,6LAAC;oDAAE,WAAU;8DAAsB,OAAO,QAAQ;;;;;;8DAElD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,6LAAC;oEAAE,WAAW,CAAC,KAAK,EAAE,OAAO,SAAS,GAAG,mBAAmB,gBAAgB;8EACzE,OAAO,UAAU,IAAI;;;;;;;;;;;;sEAG1B,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,6LAAC;oEAAE,WAAU;8EAAuB,OAAO,aAAa;;;;;;;;;;;;;;;;;;8DAI5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,6LAAC;4DAAE,WAAU;sEAAsB,OAAO,WAAW;;;;;;;;;;;;;2CAlC/C,OAAO,UAAU;;;;;;;;;;;0CAyC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,OAAO,EAAE,UAAU,GAAG;wCAC7B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;;kDAC7B,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,WAAW;;;;;;;;;;;;kDAEpB,6LAAC;wCAAI,WAAU;;4CAAwB;4CAC3B,uBAAuB;4CAAE;4CAAE,UAAU,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,UAAU,KAAK;;;;;;sDAElB,6LAAC;4CAAE,WAAU;sDAAsB,UAAU,WAAW;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAiB,UAAU,QAAQ;;;;;;;;;;;;;;;;;;8CAKpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDAAsC;wDACxC,uBAAuB;;;;;;;8DAEnC,6LAAC;oDAAK,WAAU;;wDACb,gBAAgB,MAAM;wDAAC;;;;;;;;;;;;;sDAI5B,6LAAC;4CAAE,WAAU;sDAAsB,gBAAgB,QAAQ;;;;;;wCAE1D,gBAAgB,IAAI,KAAK,wHAAA,CAAA,eAAY,CAAC,SAAS,kBAC9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO,OAAO,CAAC,gBAAgB,EAAE,CAAC,IAAI;oDACtC,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDAClD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;wCAM7C,0BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,6LAAC;gEAAE,WAAU;0EACV,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,YAAY,GAAG,UAAU,KAAK,CAAC,MAAM,GAAG,GAAG;;;;;;;;;;;;;;;;;;;;;;;sDAQ/E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,UAAU,yBAAyB;oDACnC,WAAU;8DACX;;;;;;8DAID,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,iOAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;wDAC9B;wDAAU;;;;;;;8DAGrB,6LAAC;oDACC,SAAS;oDACT,UAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;oDACtC,WAAU;8DAET,iBAAiB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAOvC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,sBAC3B,6LAAC;oDAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,uBACN,8BACA,OAAO,CAAC,UAAU,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,GACtC,gCACA,6BACJ;;sEAEF,6LAAC;;gEAAK;gEAAU,QAAQ;;;;;;;wDACvB,OAAO,CAAC,UAAU,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,kBACrC,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;mDAXpB;;;;;;;;;;;;;;;;8CAmBb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;wCAChD,UAAU,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,oBAChC,6LAAC;gDAAiB,WAAU;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAK,WAAU;0EAA6B,IAAI,IAAI;;;;;;;;;;;;kEAEvD,6LAAC;wDAAI,WAAU;kEACZ,IAAI,OAAO;;;;;;;+CANN,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBhC;GA5ZwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}