import {
  formatTime,
  formatTimeMinutes,
  calculatePercentage,
  getPerformanceLevel,
  validateNumericAnswer,
  getNextRecommendedLevel,
  calculateProgressStats,
  formatNumber,
  formatCurrency
} from '@/lib/utils';

describe('Utils', () => {
  describe('formatTime', () => {
    it('should format seconds correctly', () => {
      expect(formatTime(30)).toBe('30s');
      expect(formatTime(90)).toBe('1m 30s');
      expect(formatTime(3661)).toBe('1h 1m 1s');
      expect(formatTime(0)).toBe('0s');
    });
  });

  describe('formatTimeMinutes', () => {
    it('should format minutes correctly', () => {
      expect(formatTimeMinutes(30)).toBe('30m');
      expect(formatTimeMinutes(90)).toBe('1h 30m');
      expect(formatTimeMinutes(120)).toBe('2h 0m');
      expect(formatTimeMinutes(0)).toBe('0m');
    });
  });

  describe('calculatePercentage', () => {
    it('should calculate percentage correctly', () => {
      expect(calculatePercentage(80, 100)).toBe(80);
      expect(calculatePercentage(75, 150)).toBe(50);
      expect(calculatePercentage(0, 100)).toBe(0);
      expect(calculatePercentage(100, 100)).toBe(100);
    });

    it('should handle division by zero', () => {
      expect(calculatePercentage(50, 0)).toBe(0);
    });
  });

  describe('getPerformanceLevel', () => {
    it('should return correct performance levels', () => {
      expect(getPerformanceLevel(95)).toEqual({
        level: "Excellent",
        color: "text-green-600",
        description: "Performance exceptionnelle !"
      });

      expect(getPerformanceLevel(85)).toEqual({
        level: "Très bien",
        color: "text-blue-600",
        description: "Très bonne maîtrise"
      });

      expect(getPerformanceLevel(75)).toEqual({
        level: "Bien",
        color: "text-yellow-600",
        description: "Bonne compréhension"
      });

      expect(getPerformanceLevel(65)).toEqual({
        level: "Passable",
        color: "text-orange-600",
        description: "Compréhension correcte"
      });

      expect(getPerformanceLevel(50)).toEqual({
        level: "À améliorer",
        color: "text-red-600",
        description: "Nécessite plus de travail"
      });
    });
  });

  describe('validateNumericAnswer', () => {
    it('should validate exact numeric answers', () => {
      expect(validateNumericAnswer('100', '100', 0)).toBe(true);
      expect(validateNumericAnswer('100.5', '100.5', 0)).toBe(true);
      expect(validateNumericAnswer('100', '101', 0)).toBe(false);
    });

    it('should validate numeric answers with tolerance', () => {
      expect(validateNumericAnswer('100', '100', 5)).toBe(true);
      expect(validateNumericAnswer('105', '100', 5)).toBe(true); // 5% tolerance
      expect(validateNumericAnswer('95', '100', 5)).toBe(true);  // 5% tolerance
      expect(validateNumericAnswer('110', '100', 5)).toBe(false); // Outside 5% tolerance
      expect(validateNumericAnswer('90', '100', 5)).toBe(false);  // Outside 5% tolerance
    });

    it('should handle formatted numbers', () => {
      expect(validateNumericAnswer('1,000', '1000', 0)).toBe(true);
      expect(validateNumericAnswer('€100', '100', 0)).toBe(true);
      expect(validateNumericAnswer('100€', '100', 0)).toBe(true);
    });

    it('should handle invalid inputs', () => {
      expect(validateNumericAnswer('abc', '100', 0)).toBe(false);
      expect(validateNumericAnswer('', '100', 0)).toBe(false);
      expect(validateNumericAnswer('100', 'abc', 0)).toBe(false);
    });
  });

  describe('getNextRecommendedLevel', () => {
    it('should recommend next level for normal performance', () => {
      const result = getNextRecommendedLevel(5, 70, 100, [1, 2, 3, 4, 5]);
      expect(result).toBe(6);
    });

    it('should recommend advanced level for excellent performance', () => {
      const result = getNextRecommendedLevel(5, 90, 100, [1, 2, 3, 4, 5]);
      expect(result).toBe(15); // Jump to level 15 (5 + 10)
    });

    it('should return null when no more levels available', () => {
      const completedLevels = Array.from({length: 300}, (_, i) => i + 1);
      const result = getNextRecommendedLevel(300, 90, 100, completedLevels);
      expect(result).toBeNull();
    });

    it('should skip already completed levels', () => {
      const result = getNextRecommendedLevel(5, 70, 100, [1, 2, 3, 4, 5, 6]);
      expect(result).toBe(7);
    });
  });

  describe('calculateProgressStats', () => {
    it('should calculate progress statistics correctly', () => {
      const completedLevels = [1, 2, 3, 4, 5];
      const result = calculateProgressStats(completedLevels, 100);

      expect(result).toEqual({
        completed: 5,
        total: 100,
        percentage: 5,
        remaining: 95
      });
    });

    it('should handle empty completed levels', () => {
      const result = calculateProgressStats([], 100);

      expect(result).toEqual({
        completed: 0,
        total: 100,
        percentage: 0,
        remaining: 100
      });
    });

    it('should handle 100% completion', () => {
      const completedLevels = Array.from({length: 10}, (_, i) => i + 1);
      const result = calculateProgressStats(completedLevels, 10);

      expect(result).toEqual({
        completed: 10,
        total: 10,
        percentage: 100,
        remaining: 0
      });
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with French locale', () => {
      expect(formatNumber(1000)).toBe('1 000');
      expect(formatNumber(1234567)).toBe('1 234 567');
      expect(formatNumber(123)).toBe('123');
    });
  });

  describe('formatCurrency', () => {
    it('should format currency with EUR by default', () => {
      expect(formatCurrency(1000)).toBe('1 000,00 €');
      expect(formatCurrency(1234.56)).toBe('1 234,56 €');
    });

    it('should format currency with specified currency', () => {
      expect(formatCurrency(1000, 'USD')).toBe('1 000,00 $US');
    });
  });
});
