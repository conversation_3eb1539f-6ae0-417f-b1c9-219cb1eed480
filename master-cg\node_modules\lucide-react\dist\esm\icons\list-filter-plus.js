/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 18h4", key: "1ulq68" }],
  ["path", { d: "M11 6H3", key: "1u26ik" }],
  ["path", { d: "M15 6h6", key: "1jlkvy" }],
  ["path", { d: "M18 9V3", key: "xwwp7m" }],
  ["path", { d: "M7 12h8", key: "7a1bxv" }]
];
const ListFilterPlus = createLucideIcon("list-filter-plus", __iconNode);

export { __iconNode, ListFilterPlus as default };
//# sourceMappingURL=list-filter-plus.js.map
