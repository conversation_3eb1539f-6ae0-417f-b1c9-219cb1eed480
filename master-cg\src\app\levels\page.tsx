"use client";

import { useState } from "react";
import Link from "next/link";
import { 
  Search, 
  Filter, 
  Play, 
  Lock, 
  CheckCircle, 
  Clock, 
  Star,
  BookOpen,
  Target
} from "lucide-react";
import { LevelCategory, Difficulty } from "@/types";
import { 
  translateCategory, 
  translateDifficulty, 
  getCategoryColor, 
  getDifficultyColor 
} from "@/lib/utils";

// Données mockées pour la démonstration
const mockLevels = Array.from({ length: 50 }, (_, i) => {
  const id = i + 1;
  const categories = Object.values(LevelCategory);
  const difficulties = Object.values(Difficulty);
  
  return {
    id,
    title: `Niveau ${id} - ${getRandomTitle(id)}`,
    description: `Description du niveau ${id}`,
    category: categories[Math.floor(i / 10) % categories.length],
    difficulty: difficulties[Math.floor(i / 12) % difficulties.length],
    estimatedTime: 15 + (i % 4) * 10,
    maxScore: 100 + (i % 3) * 50,
    isCompleted: i < 14,
    isUnlocked: i < 15,
    userScore: i < 14 ? 75 + Math.floor(Math.random() * 25) : null,
    prerequisites: i === 0 ? [] : [i]
  };
});

function getRandomTitle(id: number): string {
  const titles = [
    "Calcul du coût unitaire",
    "Seuil de rentabilité",
    "Analyse des écarts",
    "Budget des ventes",
    "Marge sur coût variable",
    "Coûts préétablis",
    "Tableau de bord",
    "Ratios financiers",
    "VAN et TRI",
    "Pricing stratégique"
  ];
  return titles[id % titles.length];
}

export default function LevelsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<LevelCategory | "ALL">("ALL");
  const [selectedDifficulty, setSelectedDifficulty] = useState<Difficulty | "ALL">("ALL");

  const filteredLevels = mockLevels.filter(level => {
    const matchesSearch = level.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         level.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "ALL" || level.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === "ALL" || level.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-2xl font-bold text-blue-600">
                Master CG
              </Link>
              <nav className="hidden md:flex space-x-6">
                <Link href="/dashboard" className="text-gray-600 hover:text-gray-900">Dashboard</Link>
                <Link href="/levels" className="text-blue-600 font-medium">Niveaux</Link>
                <Link href="/progress" className="text-gray-600 hover:text-gray-900">Progression</Link>
                <Link href="/achievements" className="text-gray-600 hover:text-gray-900">Succès</Link>
              </nav>
            </div>
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-700">
              ← Retour au dashboard
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Tous les niveaux
          </h1>
          <p className="text-gray-600">
            Explorez les 300 niveaux de Master CG et progressez à votre rythme
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg p-6 shadow-sm mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher un niveau..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as LevelCategory | "ALL")}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ALL">Toutes les catégories</option>
              {Object.values(LevelCategory).map(category => (
                <option key={category} value={category}>
                  {translateCategory(category)}
                </option>
              ))}
            </select>

            {/* Difficulty Filter */}
            <select
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value as Difficulty | "ALL")}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ALL">Toutes les difficultés</option>
              {Object.values(Difficulty).map(difficulty => (
                <option key={difficulty} value={difficulty}>
                  {translateDifficulty(difficulty)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredLevels.length} niveau{filteredLevels.length > 1 ? 's' : ''} trouvé{filteredLevels.length > 1 ? 's' : ''}
          </p>
        </div>

        {/* Levels Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLevels.map((level) => (
            <div
              key={level.id}
              className={`bg-white rounded-lg p-6 shadow-sm border-2 transition-all duration-200 ${
                level.isUnlocked 
                  ? 'border-transparent hover:border-blue-300 hover:shadow-md' 
                  : 'border-gray-200 opacity-60'
              }`}
            >
              {/* Level Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-gray-500">Niveau {level.id}</span>
                    {level.isCompleted && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {!level.isUnlocked && (
                      <Lock className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {level.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {level.description}
                  </p>
                </div>
              </div>

              {/* Level Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getCategoryColor(level.category)}`}>
                    {translateCategory(level.category)}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(level.difficulty)}`}>
                    {translateDifficulty(level.difficulty)}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{level.estimatedTime} min</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Target className="h-4 w-4" />
                    <span>{level.maxScore} pts max</span>
                  </div>
                </div>

                {level.isCompleted && level.userScore && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Votre score:</span>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium text-gray-900">
                        {level.userScore}/{level.maxScore}
                      </span>
                      <span className="text-gray-600">
                        ({Math.round((level.userScore / level.maxScore) * 100)}%)
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Action Button */}
              <div className="pt-4 border-t border-gray-100">
                {level.isUnlocked ? (
                  <Link
                    href={`/level/${level.id}`}
                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  >
                    {level.isCompleted ? (
                      <>
                        <BookOpen className="mr-2 h-4 w-4" />
                        Revoir
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Commencer
                      </>
                    )}
                  </Link>
                ) : (
                  <div className="w-full inline-flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-500 rounded-lg font-medium cursor-not-allowed">
                    <Lock className="mr-2 h-4 w-4" />
                    Verrouillé
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button (pour la pagination future) */}
        {filteredLevels.length >= 50 && (
          <div className="text-center mt-12">
            <button className="px-8 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              Charger plus de niveaux
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
