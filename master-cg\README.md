# 🎯 Master CG - Plateforme d'Apprentissage du Contrôle de Gestion

Une plateforme d'apprentissage interactive avec **300 niveaux progressifs** pour maîtriser le contrôle de gestion, développée avec Next.js, TypeScript, et une IA intégrée pour un feedback personnalisé.

## ✨ Fonctionnalités Principales

### 🎮 Système de Jeu Complet
- **300 niveaux** organisés en 5 catégories thématiques
- **Progression adaptative** avec déblocage intelligent des niveaux
- **Système de scoring** avec correction automatique
- **Indices contextuels** pour guider l'apprentissage

### 🤖 Intelligence Artificielle Intégrée
- **Feedback personnalisé** généré par GPT-4
- **Corrections détaillées** avec explications pédagogiques
- **Recommandations adaptées** au niveau de l'utilisateur
- **Indices intelligents** basés sur le contexte

### 📊 Suivi de Progression Avancé
- **Dashboard personnalisé** avec statistiques détaillées
- **Système d'achievements** avec badges et récompenses
- **Analyse de performance** par catégorie
- **Historique complet** des tentatives et scores

### 🎨 Interface Moderne
- **Design responsive** optimisé pour tous les appareils
- **Expérience utilisateur fluide** avec animations
- **Thème professionnel** adapté à l'apprentissage
- **Navigation intuitive** entre les niveaux

## 🏗️ Architecture Technique

### Stack Technologique
- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Backend**: API Routes Next.js + Prisma ORM
- **Base de données**: PostgreSQL (compatible Supabase)
- **IA**: OpenAI GPT-4 pour le feedback intelligent
- **Tests**: Jest + Testing Library
- **Déploiement**: Vercel (recommandé)

### Structure des Données
```
📁 src/
├── 📁 app/                 # Pages et API routes
├── 📁 components/          # Composants réutilisables
├── 📁 lib/                 # Utilitaires et services
├── 📁 types/               # Définitions TypeScript
├── 📁 data/                # Données des niveaux
└── 📁 __tests__/           # Tests unitaires
```

## 🚀 Installation et Configuration

### Prérequis
- Node.js 18+
- PostgreSQL ou compte Supabase
- Clé API OpenAI (optionnel pour l'IA)

### Installation
```bash
# Cloner le projet
git clone [url-du-repo]
cd master-cg

# Installer les dépendances
npm install

# Configurer les variables d'environnement
cp .env.example .env.local
```

### Configuration de la Base de Données
```bash
# Générer le client Prisma
npm run db:generate

# Pousser le schéma vers la base
npm run db:push

# Initialiser avec les 300 niveaux
npm run db:seed
```

### Variables d'Environnement
```env
# Base de données
DATABASE_URL="postgresql://username:password@localhost:5432/master_cg"

# OpenAI (optionnel)
OPENAI_API_KEY="your-openai-api-key"

# Application
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## 🎯 Structure des 300 Niveaux

### Bloc 1: Fondamentaux (1-50)
- Coûts de base et calculs unitaires
- Marges et résultats d'exploitation
- Seuil de rentabilité et point mort
- Classification des charges
- Méthodes de calcul avancées

### Bloc 2: Budget et Clôture (51-100)
- Élaboration budgétaire
- Suivi et contrôle budgétaire
- Procédures de clôture
- Reporting financier

### Bloc 3: Analyse et Décision (101-180)
- Indicateurs de performance (KPIs)
- Analyse de rentabilité
- Décisions d'investissement
- Stratégies de pricing

### Bloc 4: Contrôle Industriel (181-240)
- Performance industrielle et OEE
- Gestion des coûts industriels
- Amélioration continue et Lean

### Bloc 5: Pilotage Stratégique (241-300)
- Tableaux de bord avancés
- Analyse stratégique et benchmarking
- Pilotage et gouvernance financière

## 🧪 Tests et Qualité

### Lancer les Tests
```bash
# Tests unitaires
npm test

# Tests en mode watch
npm run test:watch

# Couverture de code
npm run test:coverage
```

### Qualité du Code
```bash
# Linting
npm run lint

# Build de production
npm run build
```

## 📈 Performances et Optimisations

### Fonctionnalités d'Optimisation
- **Cache intelligent** des niveaux fréquemment accédés
- **Préchargement** des niveaux suivants
- **Lazy loading** des images et ressources
- **Virtualisation** pour les listes longues
- **Stockage optimisé** avec compression

### Monitoring
- Métriques de performance intégrées
- Suivi des temps de réponse API
- Analyse de l'utilisation du cache

## 🚀 Déploiement

### Déploiement sur Vercel (Recommandé)
```bash
# Installer Vercel CLI
npm i -g vercel

# Déployer
vercel --prod
```

### Variables d'Environnement de Production
- Configurer `DATABASE_URL` avec votre base PostgreSQL
- Ajouter `OPENAI_API_KEY` pour l'IA
- Définir `NEXTAUTH_SECRET` pour la sécurité

## 🤝 Contribution

### Structure de Développement
1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. **Commiter** les changes (`git commit -am 'Ajout nouvelle fonctionnalité'`)
4. **Pousser** vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. **Créer** une Pull Request

### Standards de Code
- TypeScript strict activé
- ESLint + Prettier pour le formatage
- Tests unitaires requis pour les nouvelles fonctionnalités
- Documentation des API avec JSDoc

## 📚 Documentation API

### Endpoints Principaux
```
GET    /api/levels/[id]           # Récupérer un niveau
POST   /api/levels/[id]/attempt   # Soumettre une tentative
GET    /api/user/progress         # Progression utilisateur
POST   /api/ai/feedback           # Générer feedback IA
```

### Modèles de Données
Voir `src/types/index.ts` pour les définitions TypeScript complètes.

## 🎓 Utilisation Pédagogique

### Pour les Enseignants
- Suivi détaillé des progrès étudiants
- Rapports de performance par catégorie
- Identification des points faibles
- Contenu adaptatif selon le niveau

### Pour les Étudiants
- Apprentissage progressif et structuré
- Feedback immédiat et personnalisé
- Gamification avec badges et récompenses
- Révision ciblée des concepts difficiles

## 📞 Support et Contact

- **Documentation**: [Lien vers la doc]
- **Issues**: [Lien vers GitHub Issues]
- **Discussions**: [Lien vers GitHub Discussions]

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**Développé avec ❤️ pour l'apprentissage du contrôle de gestion**
