"use client";

import { useState } from "react";
import Link from "next/link";
import {
  Play,
  Trophy,
  BookOpen,
  TrendingUp,
  Clock,
  Target,
  Star,
  ChevronRight,
  Calendar,
  Award,
  Zap,
  Users,
  BarChart3,
  Sparkles
} from "lucide-react";
import { Header } from "@/components/layout/Header";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { AnimatedContainer, StaggeredContainer } from "@/components/ui/AnimatedContainer";
import { InteractiveElement } from "@/components/ui/InteractiveElement";

// Données mockées pour la démonstration
const mockUserProgress = {
  currentLevel: 15,
  totalScore: 2450,
  completedLevels: 14,
  totalLevels: 300,
  streakDays: 7,
  averageScore: 85,
  timeSpent: 420, // en minutes
  lastPlayedAt: new Date(),
};

const mockRecentLevels = [
  { id: 14, title: "<PERSON>ly<PERSON> des écarts budgétaires", score: 92, maxScore: 100, completedAt: new Date() },
  { id: 13, title: "Budget de trésorerie", score: 88, maxScore: 100, completedAt: new Date(Date.now() - 86400000) },
  { id: 12, title: "Coûts préétablis", score: 95, maxScore: 100, completedAt: new Date(Date.now() - 172800000) },
];

const mockAchievements = [
  { id: 1, name: "Premier pas", icon: "🎯", unlockedAt: new Date() },
  { id: 2, name: "Série de 5", icon: "🔥", unlockedAt: new Date() },
  { id: 3, name: "Score parfait", icon: "⭐", unlockedAt: new Date() },
];

const mockUpcomingLevels = [
  { id: 15, title: "Seuil de rentabilité multi-produits", difficulty: "Intermédiaire", estimatedTime: 25 },
  { id: 16, title: "Analyse de la contribution marginale", difficulty: "Intermédiaire", estimatedTime: 30 },
  { id: 17, title: "Choix d'investissement - VAN", difficulty: "Avancé", estimatedTime: 35 },
];

export default function Dashboard() {
  const [selectedTab, setSelectedTab] = useState("overview");

  const progressPercentage = (mockUserProgress.completedLevels / mockUserProgress.totalLevels) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header Premium */}
      <Header variant="default" />

      {/* Contenu principal avec padding pour le header fixe */}
      <div className="pt-20 lg:pt-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Welcome Section Premium */}
          <AnimatedContainer animation="slide-up" delay={200}>
            <div className="mb-12">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                    Bon retour ! 👋
                  </h1>
                  <p className="text-xl text-gray-600">
                    Continuez votre parcours d'apprentissage en contrôle de gestion
                  </p>
                </div>
              </div>

              <Badge variant="premium" size="lg" className="inline-flex items-center">
                <Trophy className="w-4 h-4 mr-2" />
                Niveau {mockUserProgress.currentLevel} • {mockUserProgress.totalScore.toLocaleString()} points
              </Badge>
            </div>
          </AnimatedContainer>

          {/* Stats Cards Premium */}
          <StaggeredContainer delay={100}>
            {[
              {
                title: "Niveau actuel",
                value: mockUserProgress.currentLevel,
                icon: Target,
                color: "from-blue-500 to-cyan-500",
                bgColor: "from-blue-50 to-cyan-50",
                change: "+2 cette semaine"
              },
              {
                title: "Score total",
                value: mockUserProgress.totalScore.toLocaleString(),
                icon: Trophy,
                color: "from-emerald-500 to-teal-500",
                bgColor: "from-emerald-50 to-teal-50",
                change: "+450 cette semaine"
              },
              {
                title: "Série actuelle",
                value: `${mockUserProgress.streakDays} jours`,
                icon: Calendar,
                color: "from-amber-500 to-orange-500",
                bgColor: "from-amber-50 to-orange-50",
                change: "Record: 12 jours"
              },
              {
                title: "Score moyen",
                value: `${mockUserProgress.averageScore}%`,
                icon: TrendingUp,
                color: "from-purple-500 to-pink-500",
                bgColor: "from-purple-50 to-pink-50",
                change: "+5% ce mois"
              }
            ].map((stat, index) => (
              <InteractiveElement key={index} effect="hover-lift" intensity="medium">
                <Card variant="premium" className="group">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                          {stat.value}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
                      </div>
                      <div className={`w-14 h-14 rounded-2xl bg-gradient-to-br ${stat.bgColor} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${stat.color} flex items-center justify-center`}>
                          <stat.icon className="h-5 w-5 text-white" />
                        </div>
                      </div>
                    </div>

                    {/* Barre de progression pour certaines stats */}
                    {stat.title === "Score moyen" && (
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${mockUserProgress.averageScore}%` }}
                        ></div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </InteractiveElement>
            ))}
          </StaggeredContainer>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Progress Section */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Votre progression</h2>
                <span className="text-sm text-gray-600">
                  {mockUserProgress.completedLevels}/{mockUserProgress.totalLevels} niveaux
                </span>
              </div>
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progression globale</span>
                  <span>{progressPercentage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>
              <Link 
                href={`/level/${mockUserProgress.currentLevel}`}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                <Play className="mr-2 h-5 w-5" />
                Continuer le niveau {mockUserProgress.currentLevel}
              </Link>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Activité récente</h2>
              <div className="space-y-4">
                {mockRecentLevels.map((level) => (
                  <div key={level.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Star className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">Niveau {level.id}</h3>
                        <p className="text-sm text-gray-600">{level.title}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900">
                        {level.score}/{level.maxScore}
                      </div>
                      <div className="text-sm text-gray-600">
                        {Math.round((level.score / level.maxScore) * 100)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Next Levels */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Prochains niveaux</h2>
              <div className="space-y-3">
                {mockUpcomingLevels.map((level) => (
                  <Link
                    key={level.id}
                    href={`/level/${level.id}`}
                    className="block p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">Niveau {level.id}</h3>
                        <p className="text-sm text-gray-600">{level.title}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                            {level.difficulty}
                          </span>
                          <span className="text-xs text-gray-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {level.estimatedTime}min
                          </span>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Recent Achievements */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Succès récents</h2>
              <div className="space-y-3">
                {mockAchievements.map((achievement) => (
                  <div key={achievement.id} className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div>
                      <h3 className="font-medium text-gray-900">{achievement.name}</h3>
                      <p className="text-sm text-gray-600">Débloqué récemment</p>
                    </div>
                  </div>
                ))}
              </div>
              <Link 
                href="/achievements"
                className="block text-center mt-4 text-blue-600 hover:text-blue-700 font-medium"
              >
                Voir tous les succès
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
