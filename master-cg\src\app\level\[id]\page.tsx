"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { 
  ArrowLeft, 
  Clock, 
  HelpCircle, 
  CheckCircle, 
  XCircle,
  Lightbulb,
  FileText,
  Target
} from "lucide-react";
import { QuestionType } from "@/types";
import { SAMPLE_LEVELS } from "@/data/sample-levels";

// Données mockées pour la démonstration
const mockLevel = {
  id: 1,
  title: "Calcul du coût unitaire - Boulangerie Dupont",
  description: "Apprenez à calculer le coût unitaire d'un produit avec un cas pratique simple",
  scenario: `
La boulangerie Dupont souhaite calculer le coût unitaire de ses baguettes traditionnelles.
Vous êtes consultant en contrôle de gestion et devez l'aider dans cette démarche.
  `,
  context: {
    company: "Boulangerie Dupont",
    sector: "Boulangerie-Pâtisserie",
    situation: "Calcul de coût unitaire pour optimiser la rentabilité",
    documents: [
      {
        id: "doc1",
        name: "Fiche de coûts - Baguettes",
        type: "RAPPORT",
        content: `
COÛTS DE PRODUCTION - BAGUETTES TRADITIONNELLES
Période: Janvier 2024
Production: 1 000 baguettes

MATIÈRES PREMIÈRES:
- Farine: 200 kg à 0,80€/kg = 160€
- Levure: 2 kg à 3,50€/kg = 7€
- Sel: 3 kg à 0,30€/kg = 0,90€
- Eau: négligeable

MAIN D'ŒUVRE DIRECTE:
- Boulanger: 20h à 18€/h = 360€

CHARGES INDIRECTES:
- Électricité four: 80€
- Amortissement matériel: 50€
- Autres charges: 42,10€

TOTAL DES COÛTS: ?
COÛT UNITAIRE: ?
        `
      }
    ]
  },
  questions: [
    {
      id: "q1",
      type: QuestionType.NUMERIQUE,
      question: "Quel est le montant total des matières premières utilisées ?",
      correctAnswer: "167.90",
      explanation: "Farine (160€) + Levure (7€) + Sel (0,90€) = 167,90€",
      points: 25,
      tolerance: 0.1
    },
    {
      id: "q2",
      type: QuestionType.NUMERIQUE,
      question: "Quel est le coût total de production des 1000 baguettes ?",
      correctAnswer: "700",
      explanation: "Matières premières (167,90€) + Main d'œuvre (360€) + Charges indirectes (172,10€) = 700€",
      points: 35,
      tolerance: 0.1
    },
    {
      id: "q3",
      type: QuestionType.NUMERIQUE,
      question: "Quel est le coût unitaire d'une baguette (en euros) ?",
      correctAnswer: "0.70",
      explanation: "Coût total (700€) ÷ Quantité produite (1000) = 0,70€ par baguette",
      points: 40,
      tolerance: 0.01
    }
  ],
  maxScore: 100,
  hints: [
    "Commencez par additionner tous les coûts de matières premières",
    "N'oubliez pas d'inclure tous les types de coûts : matières, main d'œuvre et charges indirectes",
    "Le coût unitaire = Coût total ÷ Nombre d'unités produites"
  ],
  estimatedTime: 15
};

export default function LevelPage() {
  const params = useParams();
  const router = useRouter();
  const levelId = parseInt(params.id as string);
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showHint, setShowHint] = useState(false);
  const [hintsUsed, setHintsUsed] = useState(0);
  const [timeSpent, setTimeSpent] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [startTime] = useState(Date.now());

  // Timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  const currentQuestion = mockLevel.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === mockLevel.questions.length - 1;

  const handleAnswerChange = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      handleSubmit();
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
      setShowHint(false);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setShowHint(false);
    }
  };

  const handleHint = () => {
    setShowHint(true);
    setHintsUsed(prev => prev + 1);
  };

  const validateAnswer = (question: any, userAnswer: string) => {
    if (question.type === QuestionType.NUMERIQUE) {
      const userNum = parseFloat(userAnswer.replace(/[^\d.-]/g, ''));
      const correctNum = parseFloat(question.correctAnswer.replace(/[^\d.-]/g, ''));
      
      if (isNaN(userNum) || isNaN(correctNum)) return false;
      
      const tolerance = question.tolerance || 0;
      const diff = Math.abs(userNum - correctNum);
      const toleranceValue = Math.abs(correctNum * tolerance / 100);
      
      return diff <= toleranceValue;
    }
    
    return userAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
  };

  const handleSubmit = () => {
    let totalScore = 0;
    const questionResults = mockLevel.questions.map(question => {
      const userAnswer = answers[question.id] || "";
      const isCorrect = validateAnswer(question, userAnswer);
      const pointsEarned = isCorrect ? question.points : 0;
      totalScore += pointsEarned;
      
      return {
        questionId: question.id,
        question: question.question,
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        pointsEarned,
        explanation: question.explanation
      };
    });

    const finalResults = {
      totalScore,
      maxScore: mockLevel.maxScore,
      percentage: Math.round((totalScore / mockLevel.maxScore) * 100),
      timeSpent,
      hintsUsed,
      questions: questionResults
    };

    setResults(finalResults);
    setIsCompleted(true);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isCompleted && results) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <Link href="/dashboard" className="flex items-center text-blue-600 hover:text-blue-700">
                <ArrowLeft className="mr-2 h-5 w-5" />
                Retour au dashboard
              </Link>
              <div className="text-sm text-gray-600">
                Niveau {levelId} terminé
              </div>
            </div>
          </div>
        </header>

        {/* Results */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <div className="text-center mb-8">
              <div className={`w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${
                results.percentage >= 80 ? 'bg-green-100' : 
                results.percentage >= 60 ? 'bg-yellow-100' : 'bg-red-100'
              }`}>
                {results.percentage >= 80 ? (
                  <CheckCircle className="h-10 w-10 text-green-600" />
                ) : results.percentage >= 60 ? (
                  <Target className="h-10 w-10 text-yellow-600" />
                ) : (
                  <XCircle className="h-10 w-10 text-red-600" />
                )}
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Niveau terminé !
              </h1>
              
              <div className="text-6xl font-bold mb-4">
                <span className={
                  results.percentage >= 80 ? 'text-green-600' : 
                  results.percentage >= 60 ? 'text-yellow-600' : 'text-red-600'
                }>
                  {results.percentage}%
                </span>
              </div>
              
              <p className="text-xl text-gray-600">
                {results.totalScore}/{results.maxScore} points
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{formatTime(results.timeSpent)}</div>
                <div className="text-sm text-gray-600">Temps passé</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{results.hintsUsed}</div>
                <div className="text-sm text-gray-600">Indices utilisés</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">
                  {results.questions.filter((q: any) => q.isCorrect).length}
                </div>
                <div className="text-sm text-gray-600">Bonnes réponses</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{mockLevel.questions.length}</div>
                <div className="text-sm text-gray-600">Questions total</div>
              </div>
            </div>

            {/* Question Details */}
            <div className="space-y-4 mb-8">
              <h2 className="text-xl font-semibold text-gray-900">Détail des réponses</h2>
              {results.questions.map((result: any, index: number) => (
                <div key={result.questionId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-medium text-gray-900">Question {index + 1}</h3>
                    <div className={`flex items-center space-x-1 ${
                      result.isCorrect ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {result.isCorrect ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <XCircle className="h-5 w-5" />
                      )}
                      <span className="font-medium">
                        {result.pointsEarned}/{mockLevel.questions[index].points} pts
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-3">{result.question}</p>
                  
                  <div className="grid md:grid-cols-2 gap-4 mb-3">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Votre réponse:</span>
                      <p className={`mt-1 ${result.isCorrect ? 'text-green-700' : 'text-red-700'}`}>
                        {result.userAnswer || "Pas de réponse"}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Réponse correcte:</span>
                      <p className="mt-1 text-green-700">{result.correctAnswer}</p>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 p-3 rounded">
                    <span className="text-sm font-medium text-blue-800">Explication:</span>
                    <p className="mt-1 text-blue-700">{result.explanation}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/dashboard"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center"
              >
                Retour au dashboard
              </Link>
              <Link
                href={`/level/${levelId + 1}`}
                className="px-6 py-3 border border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center"
              >
                Niveau suivant
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
              >
                Recommencer
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/levels" className="flex items-center text-blue-600 hover:text-blue-700">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Retour aux niveaux
            </Link>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{formatTime(timeSpent)}</span>
              </div>
              <div className="text-sm text-gray-600">
                Question {currentQuestionIndex + 1}/{mockLevel.questions.length}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Level Info */}
            <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {mockLevel.title}
              </h1>
              <p className="text-gray-600 mb-4">{mockLevel.description}</p>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h2 className="font-semibold text-blue-900 mb-2">Scénario</h2>
                <p className="text-blue-800">{mockLevel.scenario}</p>
              </div>
            </div>

            {/* Question */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  Question {currentQuestionIndex + 1}
                </h2>
                <span className="text-sm text-gray-600">
                  {currentQuestion.points} points
                </span>
              </div>
              
              <p className="text-gray-700 mb-6">{currentQuestion.question}</p>
              
              {currentQuestion.type === QuestionType.NUMERIQUE && (
                <div className="mb-6">
                  <input
                    type="text"
                    value={answers[currentQuestion.id] || ""}
                    onChange={(e) => handleAnswerChange(e.target.value)}
                    placeholder="Entrez votre réponse numérique"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                  />
                  <p className="mt-2 text-sm text-gray-600">
                    Entrez uniquement la valeur numérique (ex: 167.90)
                  </p>
                </div>
              )}

              {showHint && (
                <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <Lightbulb className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-yellow-800">Indice</h3>
                      <p className="text-yellow-700 mt-1">
                        {mockLevel.hints[Math.min(hintsUsed - 1, mockLevel.hints.length - 1)]}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation */}
              <div className="flex items-center justify-between">
                <button
                  onClick={handlePrevious}
                  disabled={currentQuestionIndex === 0}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Précédent
                </button>

                <button
                  onClick={handleHint}
                  className="flex items-center px-4 py-2 border border-yellow-300 text-yellow-700 rounded-lg font-medium hover:bg-yellow-50 transition-colors"
                >
                  <HelpCircle className="mr-2 h-4 w-4" />
                  Indice ({hintsUsed})
                </button>

                <button
                  onClick={handleNext}
                  disabled={!answers[currentQuestion.id]}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLastQuestion ? "Terminer" : "Suivant"}
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Progress */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4">Progression</h3>
              <div className="space-y-2">
                {mockLevel.questions.map((_, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-2 rounded ${
                      index === currentQuestionIndex
                        ? 'bg-blue-100 text-blue-800'
                        : answers[mockLevel.questions[index].id]
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    <span>Question {index + 1}</span>
                    {answers[mockLevel.questions[index].id] && (
                      <CheckCircle className="h-4 w-4" />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Documents */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4">Documents</h3>
              {mockLevel.context.documents.map((doc) => (
                <div key={doc.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="h-4 w-4 text-gray-600" />
                    <span className="font-medium text-gray-900">{doc.name}</span>
                  </div>
                  <pre className="text-xs text-gray-700 whitespace-pre-wrap bg-gray-50 p-3 rounded overflow-x-auto">
                    {doc.content}
                  </pre>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
